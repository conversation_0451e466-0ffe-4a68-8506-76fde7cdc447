import { CategoryService } from 'src/app/shared/services/category.service';
import { UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl } from '@angular/forms';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { ServicesService } from 'src/app/shared/services/services.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { Ng2ImgMaxService } from 'ng2-img-max';
import { Category } from 'src/app/shared/models/category.model';
import { FileUploadService } from 'src/app/shared/services/file-upload.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { combineLatest, Subscription } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { map } from 'rxjs/operators';
import { DomSanitizer } from '@angular/platform-browser';
import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import '@ckeditor/ckeditor5-build-classic/build/translations/fr';

@Component({
  selector: 'app-add',
  templateUrl: './add.component.html',
  styleUrls: ['./add.component.scss']
})
export class AddComponent implements OnInit, OnDestroy {
  Editor = ClassicEditor;
  csconfig = {
    placeholder: 'Write Summary...',
  };
  form: UntypedFormGroup;
  services = [];
  categories = [];
  selectedLevel: any;
  selectedCategory: any;
  submitted = false;
  toggleHelperText = true;
  parentId: any;
  uploadedImage: Blob;
  selectedFiles: any;
  images: any;
  finalImages = [{ name: 'topic', value: '' }, { name: 'badge', value: '' }];
  colorValue = '';
  showUpload = false;
  imageUrl: any;
  badgeUrl: any;
  topicDetails = true;
  languages = [];
  nonWhitespaceRegExp = new RegExp('\\S');
  totalCount: number;
  selectedImages = [];
  config = {
    displayKey: 'name',
    search: true,
    placeholder: 'Choose services',
    searchPlaceholder: 'Search service'
  };
  subscription: Subscription = new Subscription();

  constructor(
    private fb: UntypedFormBuilder,
    private serviceService: ServicesService,
    private ng2ImgMax: Ng2ImgMaxService,
    private categoryService: CategoryService,
    private activeModal: NgbActiveModal,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private fileUploadService: FileUploadService,
    private sanitizer: DomSanitizer,
    private spinner: NgxSpinnerService) { }

  ngOnInit() {
    this.createForm();
    // this.showSpinner();
    this.images = [{ name: 'topic', url: 'https://via.placeholder.com/1000' }, { name: 'badge', url: 'https://via.placeholder.com/150' }];
    const categories$ = combineLatest([this.getService(), this.getCategory()]).subscribe((data: any) => {
      this.services = data[0].filter((service: any) => service.isActive);      
      this.categories = data[1];
      this.afterCategory();
    }, (error) => {
      this.spinner.hide();
      throw error;
    });
    this.subscription.add(categories$);
  }


  createForm() {
    this.form = this.fb.group({
      categoryName: ['', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]],
      level: [{ value: this.selectedLevel, disabled: false }, Validators.required],
      services: [{ value: '', disabled: this.selectedCategory ? false : false }, Validators.required],
      // tslint:disable-next-line:triple-equals
      parent: [this.parentId ? { value: this.parentId.parentId, disabled: this.selectedLevel == 2 && this.selectedCategory ? false : true }
        // tslint:disable-next-line:triple-equals
        : { value: null, disabled: this.selectedLevel == 2 && this.selectedCategory ? false : true }],
      language: this.createLanguageForm(this.languages),
      activate: [true],
      summary: [''],
      summaryLanguage: this.createSummaryLanguageForm(this.languages),
      descriptionLanguage: this.createDescriptionLanguageForm(this.languages),
      description: ['']
    });
    if (this.selectedLevel === 0) {
      this.showUpload = true;
      this.form.setControl('topicImage', new UntypedFormControl('', [Validators.required]));
      this.form.setControl('badgeImage', new UntypedFormControl('', [Validators.required]));
      this.form.setControl('color', new UntypedFormControl('', [Validators.required]));

    }
  }
  createLanguageForm(languages: any[]) {
    const languageControls = {};
    languages.forEach(item => {
      languageControls[item.identifier] = ['', [Validators.minLength(2), Validators.maxLength(50),
      Validators.pattern(this.nonWhitespaceRegExp)]];
    });
    return this.fb.group(languageControls);
  }
  createSummaryLanguageForm(languages: any[]) {
    const summaryControls = {};
    languages.forEach(item => {
      summaryControls[item.identifier] = [''];
    });
    return this.fb.group(summaryControls);
  }
  createDescriptionLanguageForm(languages: any[]) {
    const descriptionControls = {};
    languages.forEach(item => {
      descriptionControls[item.identifier] = [''];
    });
    return this.fb.group(descriptionControls);
  }

  showSpinner() {
    this.spinner.show(undefined, {
      type: 'ball-fussion',
      size: 'small',
      bdColor: 'rgb(255, 255, 255)',
      color: 'rgb(42 41 92)',
      fullScreen: false
    });
  }
  get g() { return this.form.controls; }
  getService() {
    return this.serviceService.getRecords();
  }
  getCategory() {
    return this.categoryService.getCategory();
  }
  afterCategory() {
    let parentId;
    if (this.selectedCategory) {
      if (this.selectedLevel !== 0) {
        parentId = (this.selectedCategory.parentId !== null && typeof this.selectedCategory.parentId === 'object') ?
          this.selectedCategory.parentId.id : this.selectedCategory.parentId;
        this.form.patchValue({ parent: parentId });
      } else {
        parentId = null;
        this.colorValue = this.selectedCategory.color;
        this.form.patchValue({ color: this.selectedCategory.color });
      }
      this.patchForm();
    } else {
      parentId = this.parentId ? this.parentId.parentId : null;
      if (this.selectedLevel !== 0 && !!parentId) {
        this.form.patchValue({ parent: parentId });
      }

    }
    const item = this.categories.find(k => {
      // tslint:disable-next-line:triple-equals
      return k.id == parentId;
    });
    // if (item) { this.services = item.services; }
    if (this.selectedCategory) {
      // const service = [];
      // this.selectedCategory.services.forEach(element => {
      //   this.services.forEach(temp => {
      //     // tslint:disable-next-line:triple-equals
      //     if (element.id == temp.id) {
      //       service.push(element);
      //     }
      //   });
      // });
      // this.form.patchValue({
      //   services: service
      // });
      // this.services = this.selectedCategory.services;
      this.form.patchValue({
        services: this.selectedCategory.services
      });
      // this.services = item.services;

      // this.form.patchValue({
      //   services: this.services
      // });
    }
    this.categories = this.categories.filter(data => {
      data.services = data.services.map(i => {
        return JSON.stringify(i);
      });
      if (this.selectedLevel === 2) {
        // tslint:disable-next-line:triple-equals
        if (data.level === (this.selectedLevel - 1) && item.parentId == data.parentId) {
          if (this.selectedCategory) {  
            const temp = this.services.every(i => data.services.includes(JSON.stringify(i)));
            if (temp) {
              data.services = data.services.map(i => {
                return JSON.parse(i);
              });
              return data;
            }

          } else {
            data.services = data.services.map(i => {
              return JSON.parse(i);
            });
            return data;
          }
        }
      } else {
        if (data.level === (this.selectedLevel - 1)) {
          data.services = data.services.map(i => {
            return JSON.parse(i);
          });
          return data;
        }
      }

    });
    this.spinner.hide();
  }

  // Prevention of entering white spaces
  preventWhiteSpaces(e) {
    if (e.which === 32 && e.target.selectionStart === 0) {
      e.preventDefault();
    }
  }

  add() {
    this.submitted = true;
    this.toggleHelperText = false;
    if (this.form.valid) {
      this.showSpinner();
      if (this.selectedLevel === 0) {
        this.uploadFile();
      } else {
        this.upsertTopic();
      }
    } else {
      this.topicDetails = true;
    }
  }
  uploadFile() {
    const upload = this.finalImages.some(k => !!k.value);
    if (upload) {
      this.finalImages.forEach(data => {
        if (!!data.value) {
          const formData = new FormData();
          formData.append('file', data.value);
          const photoObject = this.fileUploadService.uploadFile(formData).pipe(map((eve: any) => {
            return { name: data.name, url: eve.url };
          }));
          this.selectedImages.push(photoObject);
        }
      });
      const photos$ = combineLatest(this.selectedImages).subscribe(data => {
        if (data.length > 0) {
          const badge: any = data.find((item: any) => item.name === 'badge');
          const topic: any = data.find((item: any) => item.name === 'topic');
          this.badgeUrl = !!badge ? badge.url : this.badgeUrl;
          this.imageUrl = !!topic ? topic.url : this.imageUrl;
          this.upsertTopic();
        }
      });
      this.subscription.add(photos$);
    } else {
      this.upsertTopic();
    }
  }
  upsertTopic() {
    const payload = {
      name: this.form.value.categoryName,
      level: this.form.get('level').value,
      parentId: this.selectedLevel === 0 ? null : this.form.get('parent').value,
      services: this.form.get('services').value.map(item => item.id),
      imageUrl: this.imageUrl,
      badgeUrl: this.badgeUrl,
      language: this.form.value.language,
      sequence: this.totalCount,
      color: this.colorValue,
      isActive: this.form.value.activate,
      summary: this.form.value.summary,
      Discription: this.form.value.description
    };
    payload.language.en = this.form.value.categoryName;
    const summaryMutlilanguage = {};
    const descriptionMutlilanguage = {};

    this.languages.map(i => {
      summaryMutlilanguage[i.identifier] = this.form.value.summaryLanguage[i.identifier];
      // tslint:disable-next-line:max-line-length
      descriptionMutlilanguage[i.identifier] = i.identifier === 'en' ? this.form.value.description : this.form.value.descriptionLanguage[i.identifier];
    });
    payload.language.summary = summaryMutlilanguage;
    payload.language.description = descriptionMutlilanguage;
    this.showSpinner();
    if (this.selectedCategory) {
      const category$ = this.categoryService.updateCategory(this.selectedCategory.id, payload).subscribe((res: Category) => {
        this.spinner.hide();
        this.toastr.success('Topic updated successfully');
        this.activeModal.close(res);
      }, error => {
        this.spinner.hide();
        this.close();
        throw error;
      });
      this.subscription.add(category$);
    } else {
      const category$ = this.categoryService.createCategory(payload).subscribe((res: Category) => {
        this.spinner.hide();
        this.toastr.success('Topic added successfully');
        this.activeModal.close(res);
      }, error => {
        this.spinner.hide();
        this.close();
        throw error;
      });
      this.subscription.add(category$);

    }
  }
  close() {
    this.activeModal.dismiss();
  }
  patchForm() {
    const service = [];
    this.selectedCategory.services.forEach((element: any) => {
      service.push(element);
    });
    this.form.patchValue({
      categoryName: this.selectedCategory.name,
      level: this.selectedCategory.level,
      services: service,
      topicImage: this.selectedCategory.imageUrl ? this.selectedCategory.imageUrl : '',
      badgeImage: this.selectedCategory.badgeUrl ? this.selectedCategory.badgeUrl : '',
      language: this.selectedCategory.language,
      activate: this.selectedCategory.isActive,
      summary: this.selectedCategory.summary || '',
      summaryLanguage: this.selectedCategory.language.summary ? this.selectedCategory.language.summary : {},
      descriptionLanguage: this.selectedCategory.language.description ? this.selectedCategory.language.description : {},
      description: this.selectedCategory.Discription
    });
    this.imageUrl = this.selectedCategory.imageUrl;
    this.badgeUrl = this.selectedCategory.badgeUrl;
    this.images[0].url = this.selectedCategory.imageUrl || this.images[0].url;
    this.images[1].url = this.selectedCategory.badgeUrl || this.images[1].url;
    this.spinner.hide();
  }
  fileChange(event, type) {
    if (event.target.files.length > 0) {
      // this.images = [];
      if (type === 'topic') {
        this.form.patchValue({ topicImage: event.target.files[0].name });
        this.finalImages[0].value = event.target.files[0];
      } else {
        this.form.patchValue({ badgeImage: event.target.files[0].name });
        this.finalImages[1].value = event.target.files[0];
      }
      // this.form.patchValue({ file: event.target.files[0].name });
      const selectedFiles = event.target.files[0];
      const images$ = this.ng2ImgMax.resizeImage(selectedFiles, 400, 300).subscribe(
        result => {
          this.uploadedImage = result;
        },
        error => {
        }
      );
      this.subscription.add(images$);
      const reader = new FileReader();
      reader.readAsDataURL(selectedFiles);
      reader.onload = (res: any) => {
        if (type === 'topic') {
          this.images[0].url = res.target.result;
          this.images[0].url = this.sanitizer.bypassSecurityTrustUrl(this.images[0].url);
        } else {
          this.images[1].url = res.target.result;
          this.images[1].url = this.sanitizer.bypassSecurityTrustUrl(this.images[1].url);
        }
      };
    }
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
  trackByFn(index, item) {
    return index;
  }
  colorChanged() {
    this.form.patchValue({ color: this.colorValue });
  }
  getChangedParent() {
    return;
    this.services = [];
    this.form.patchValue({ services: [] });
    const services = this.categories.find(k => {
      // tslint:disable-next-line:triple-equals
      return k.id == this.form.value.parent;
    });
    this.services = services.services;
  }
}
