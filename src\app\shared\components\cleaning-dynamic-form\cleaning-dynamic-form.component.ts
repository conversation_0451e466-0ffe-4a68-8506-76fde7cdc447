import { ChangeDetectorRef, Component, EventEmitter, Input, Output, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators, AbstractControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { EmissionfactorserviceService } from 'src/app/carbon/services/emissionfactorservice.service';
import { EmissiontypeService } from 'src/app/carbon/services/emissiontype.service';
import { GeneralInfoService } from 'src/app/carbon/services/general-info.service';
import { LanguageTranslateService } from 'src/app/carbon/services/language-translate.service';
import { QuestionfromseaService } from 'src/app/carbon/services/questionfromsea.service';

@Component({
  selector: 'app-cleaning-dynamic-form',
  templateUrl: './cleaning-dynamic-form.component.html',
  styleUrls: ['./cleaning-dynamic-form.component.scss']
})
export class CleaningDynamicFormComponent {
  @ViewChild('errorPopUP') errorPopUP: TemplateRef<any>;
  unitForEnergy = 'kWh';
  form: UntypedFormGroup = this.fb.group({});
  submitted = false;
  // @Input() jsonForm: any;
  jsonForm: any;
  @Output() jsonQestionValue = new EventEmitter();
  @Output() emitChange = new EventEmitter();
  @Output() emitYesOrNo = new EventEmitter();
  @Output() emitChangeOnChange = new EventEmitter();
  resultData: any[] = [];
  topic: any;
  language = this.service.languageIdentifier;
  dialogRef: any;
  electricResidualMixEmission: any;
  @Input()
  set jsonFormData(data: any) {
    if (data) {
      // if (this.questionTopic === 'energy') {
      //   this.getQuestionFromSea(data);
      // } else {
      this.jsonForm = data;
      this.createDynamicForm();
      // }
    }
  }
  get jsonFormData() {
    return this.jsonForm;
  }
  @Input()
  set submitForm(data: any) {
    if (data) {
      // this.submitted = data;
      this.onSubmit();
    }
  }
  get submitForm() {
    return this.submitted;
  }
  @Input()
  set questionResult(data: any) {
    this.resultData = data;
  }
  get questionResult() {
    return this.resultData;
  }
  @Input()
  set questionTopic(data: any) {
    this.topic = data;
  }
  get questionTopic() {
    return this.topic;
  }
  emissiontypelist: any;
  electricResidualMix: any;
  subscription: Subscription = new Subscription();
  config = {
    displayFn: (item: any) => item.name,
    search: false,
    height: '400px',
    placeholder: 'Choose any one',
    displayKey: 'name',
  };
  constructor(
    private fb: UntypedFormBuilder,
    private cd: ChangeDetectorRef,
    private emissionService: EmissionfactorserviceService,
    private emissiontype: EmissiontypeService,
    private modalService: NgbModal,
    public service: GeneralInfoService,
    private questionfromsea: QuestionfromseaService,
    private translate: TranslateService,
    private lts: LanguageTranslateService) {
    translate.use('en');
    translate.setTranslation('en', this.lts.state);
     }

  ngOnInit(): void {
    if (this.questionTopic === 'energy') {
      const emissiontypelist$ = this.emissiontype.getEmissionTypeAll().subscribe((res: any) => {
        this.emissiontypelist = res;
        (this.emissionService.carbonEmissionFactor || []).find((e: any) => {
          this.emissiontypelist.forEach((list: any) => {
            if (list.name === 'Electricity - residual mix' && list.type === 'energy' && list.id === e.emission_type) {
              this.electricResidualMix = list;
              this.electricResidualMixEmission = e;
              return true;
            }
          });
        });
      });
      this.subscription.add(emissiontypelist$);
    }
  }
  getQuestionFromSea(data) {
    this.questionfromsea.getQuestionFromSea(this.service.siteId, {
      questionId: ['EM-GJR']
    }).subscribe((res: any) => {
      const answer = res.questions[0].answer;
      if (!answer) {
        this.jsonForm = [...data];
        this.createDynamicForm();
      }
      else if (answer) {
        const name = answer.name;
        if (name.includes('No other energy consumption')) {
          const newQuestionData = data.filter((ques: any) => {
            const quesCondition = ques.question_condition;
            if (!quesCondition.length) {
              return true;
            }
          });
          this.jsonForm = [...newQuestionData];
          this.createDynamicForm();
        } else {
          const newQuestionData = data.filter((ques: any) => {
            const quesCondition = ques.question_condition;
            if (!quesCondition.length) {
              return true;
            } else {
              return ques.quesCondition.includes(name);
            }
          });
          this.jsonForm = [...newQuestionData];
          this.createDynamicForm();
        }
      }
      this.cd.detectChanges();
    });
  }
  ngOnChanges(changes: SimpleChanges): void {
  }
  createDynamicForm() {
    if (this.jsonForm) {
      if (this.questionTopic === 'energy') {
        this.jsonForm.forEach((question: any) => {
          if (question.emission_type != null) {
            question.tCO2eLB = '';
            question.tCO2eMB = '';
          }
        });
      }
      this.renderForm(this.jsonForm);
    }
  }
  renderForm(controls: any) {
    if (this.resultData.length > 0) {
      // mapping each question and answer and patching
      this.jsonForm.forEach((control: any) => {
        this.resultData.map((result: any) => {
          if (result.question === control.id) {
            if (this.questionTopic === 'energy') {
              const answer = JSON.parse(result.answer);
              if (answer.hasOwnProperty('tCO2eLB')) {
                control.tCO2eLB = answer.tCO2eLB;
              }
              if (answer.hasOwnProperty('tCO2eMB')) {
                control.tCO2eMB = answer.tCO2eMB;
              }
              if (answer.hasOwnProperty('unit')) {
                this.unitForEnergy = answer.unit;
              }
              if (answer?.formValue === 'Yes' || answer?.formValue === 'No') {
                this.emitYesOrNo.emit(answer?.formValue === 'Yes' ? true : false);
              }
              try {
                // Get the form value, ensuring we handle both string and numeric values properly
                const formValue = answer?.formValue !== undefined && answer?.formValue !== null ? answer.formValue : '';

                if (control.is_required) {
                  this.form.addControl(control.id, this.fb.control(formValue, Validators.required));
                } else {
                  this.form.addControl(control.id, this.fb.control(formValue));
                }
              } catch (e) {
                const formValue = answer?.formValue !== undefined && answer?.formValue !== null ? answer.formValue : '';

                if (control.is_required) {
                  this.form.addControl(control.id, this.fb.control(formValue, Validators.required));
                } else {
                  this.form.addControl(control.id, this.fb.control(formValue));
                }
              }
            } else {
              const answer = JSON.parse(result.answer);
              try {
                // Get the form value, ensuring we handle both string and numeric values properly
                const formValue = answer?.formValue !== undefined && answer?.formValue !== null ? answer.formValue : '';

                if (control.is_required) {
                  this.form.addControl(control.id, this.fb.control(formValue, Validators.required));
                } else {
                  this.form.addControl(control.id, this.fb.control(formValue));
                }
              } catch (e) {
                const formValue = answer?.formValue !== undefined && answer?.formValue !== null ? answer.formValue : '';

                if (control.is_required) {
                  this.form.addControl(control.id, this.fb.control(formValue, Validators.required));
                } else {
                  this.form.addControl(control.id, this.fb.control(formValue));
                }
              }
            }
          }
        });
      });
      // for uncreated form controls
      const keys = Object.keys(this.form.value).map((key: any) => {
        // tslint:disable-next-line:radix
        return parseInt(key);
      });
      // creating a formcontrols for uncreated controls that is missed during result patching
      const unpushcontrol = this.jsonForm.filter((un: any) => !keys.includes(un.id));
      if (unpushcontrol.length > 0) {
        unpushcontrol.map((unControl: any) => {
          if (unControl.is_required) {
            this.form.addControl(unControl.id, this.fb.control('', Validators.required));
          } else {
            this.form.addControl(unControl.id, this.fb.control(''));
          }
        });
      }
      if (this.questionTopic === 'energy') {
        this.jsonForm.map((form: any) => {
          this.resultData.map((data: any) => {
            if (form.sequence === 14 && form.id === data.question) {
              form.options.map((option: any) => {
                const data01 = JSON.parse(data.answer)?.formValue;
                const names = [];
                if (data01.length) {
                  data01.map((res: any) => {
                    names.push(res.name);
                  });
                }
                if (!names.includes(option.name)) {
                  this.form.get([option.logicQid])?.disable();
                }
              });
            }
          });
        });
      }
    } else {
      controls.map((control) => {
        if (this.questionTopic === 'waste') {
          if (control.is_required) {
            this.form.addControl(control.id, this.fb.control(control.answer ? control.answer : '', Validators.required));
          } else {
            this.form.addControl(control.id, this.fb.control(control.answer ? control.answer : ''));
          }
        } else {
          if (control.is_required) {
            this.form.addControl(control.id, this.fb.control('', Validators.required));
          } else {
            this.form.addControl(control.id, this.fb.control(''));
          }
        }
      });
      if (this.questionTopic === 'energy') {
        controls.map((control) => {
          if (control.sequence === 14) {
            control.options.map((con: any) => {
              this.form.get([con.logicQid])?.disable();
            });
          }
        });
      }
    }
  }
  get f(): { [key: string]: AbstractControl; } {
    return this.form.controls;
  }
  optionsChange(event: any, control: any) {
    this.emitChangeOnChange.emit({condition: event === 'yes' ? true : false, control});
    this.jsonQestionValue.emit({condition: event === 'yes' ? true : false, control});
  }
  emissionValueCalc(value: any, control) {
    //  || control.sequence === 3 || control.sequence === 4
    if (control.emission_type != null) {
      const emissionTypeid = control?.emission_type;
      const emissionValue = (this.emissionService.carbonEmissionFactor || []).find((e: any) => e.emission_type === emissionTypeid);
      let cal = 0;
      let cal2 = 0;
      if (control.sequence === 2 || control.sequence === 3 || control.sequence === 4) {
        // tslint:disable-next-line:one-variable-per-declaration
        let c7, c8, c9;
        this.jsonForm.map((res: any) => {
          if (res.sequence === 2) {
            c7 = res;
          }
          if (res.sequence === 3) {
            c8 = res;
          }
          if (res.sequence === 4) {
            c9 = res;
          }
        });
        const emission2Value = (this.emissionService.carbonEmissionFactor || []).find((e: any) => e.emission_type === c8.emission_type);
        // tslint:disable-next-line:max-line-length
        const electricResidualMixvalue = (this.emissionService.carbonEmissionFactor || []).find((e: any) => e.emission_type === this.electricResidualMix.id);
        const c7value = this.form.get(`${c7.id}`)?.value || 0;
        let c8Value = this.form.get(`${c8.id}`)?.value || 0;
        let c9Value = this.form.get(`${c9.id}`)?.value || 0;
        if (control.sequence === 3 || control.sequence === 4) {
          if (c8Value > 100) {
            this.showErrorMessage();
            return;
          }
          if (c9Value > 100) {
            this.showErrorMessage();
            return;
          }
          c8Value /= 100;
          c9Value /= 100;
        }
        // tslint:disable-next-line:max-line-length
        cal2 = ((c8Value * emission2Value.value) + (c9Value * emission2Value.value) + ((c7value - c8Value - c9Value) * electricResidualMixvalue.value)) / 1000;
        this.jsonForm.map((res: any, id: any) => {
          if (res.id === c7.id) {
            this.jsonForm[id].tCO2eMB = cal2.toFixed(3);
          }
        });
      }
      if (control.sequence !== 3 && control.sequence !== 4) {
        let unitValue = value;
        if (control.sequence === 6 && this.unitForEnergy === 'm3') {
          // tslint:disable-next-line:no-unused-expression
          unitValue = (unitValue * 10.55);
        }
        cal = (unitValue * (emissionValue?.value || 0)) / 1000;
        this.jsonForm.map((res: any, id: any) => {
          if (res.id === control.id) {
            this.jsonForm[id].tCO2eLB = cal.toFixed(3);
          }
        });
      }
    }
  }
  onUnitChange(index: number, control: any) {
    const value = this.form.get(`${this.jsonForm[index].id}`).value;
    this.emissionValueCalc(value, control);
  }
  onSubmit() {
    this.submitted = true;
    if (this.form.valid) {
      if (this.questionTopic === 'energy') {
        this.jsonQestionValue.emit({ formvalue: this.form.value, calc: this.jsonForm, unit: this.unitForEnergy });
      } else {
        this.jsonQestionValue.emit(this.form.value);
      }
      this.submitted = false;
    }
  }
  showErrorMessage() {
    this.dialogRef = this.modalService.open(this.errorPopUP, { centered: true });
  }
  close() {
    this.dialogRef.close();
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  getContent(content){
    // tslint:disable-next-line:no-string-literal
    return content.language[this.language] ? content.language[this.language] : content.language['en'];
  }
}
