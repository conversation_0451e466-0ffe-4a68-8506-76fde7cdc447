import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { LanguageService } from 'src/app/shared/services/language.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-add',
  templateUrl: './add.component.html',
  styleUrls: ['./add.component.scss']
})
export class AddComponent implements OnInit, OnDestroy {
  addForm: UntypedFormGroup;
  submitted = false;
  id: any;
  edited = false;
  subscription: Subscription = new Subscription();
  breadCumsData: any;
  pageOffset: any;

  constructor(private formBuilder: UntypedFormBuilder,
              private languageList: LanguageService,
              private router: Router,
              private route: ActivatedRoute,
              private spinner: NgxSpinnerService,
              private toaster: ToastrService
  ) { }
  ngOnInit() {
    this.pageOffset = this.route.snapshot.queryParams.offset;
    this.breadCumsData = {
      limit: 10,
      offset: this.pageOffset,
      search: '',
      sortName: 'createdAt',
      sortValue: 'desc'
    };
    this.route.params.subscribe((res: any) => {
      if (res.id) {
        this.edited = true;
        this.id = Number(res.id);
        this.patchForm();
      }
    });
    this.createForm();
  }

  createForm() {
    const nonWhitespaceRegExp = new RegExp('\\S');
    this.addForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.pattern(nonWhitespaceRegExp)]],
      identifier: ['', [Validators.required, Validators.pattern(nonWhitespaceRegExp)]],
    });
  }

  // validate part
  get f() {
    return this.addForm.controls;
  }

  create() {
    this.submitted = true;
    if (this.addForm.invalid) {
      return;
    } else {
      this.spinner.show();
      this.addForm.value.identifier = this.addForm.value.identifier;
      let languages$;
      if (this.id) {
        languages$ = this.languageList.updateRecord(this.id, this.addForm.value).subscribe((res) => {
          this.spinner.hide();
          this.toaster.success('Language updated successfully');
          this.listRecord();
        }, error => {
          this.spinner.hide();
          throw error;
        });
      } else {
        languages$ = this.languageList.createRecord(this.addForm.value).subscribe((res) => {
          this.spinner.hide();
          this.toaster.success('Language added successfully');
          this.listRecord();
        }, error => {
          this.spinner.hide();
          throw error;
        });
      }

      this.subscription.add(languages$);
    }
  }

  patchForm() {
    this.spinner.show();
    const language$ = this.languageList.getRecordById(this.id).subscribe((res: any) => {
      this.addForm.patchValue({
        name: res.name,
        identifier: res.identifier
      });
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      this.listRecord();
      throw error;
    });
    this.subscription.add(language$);
  }

  listRecord() {
    this.router.navigate(['admin/languages'], {
      queryParams: this.breadCumsData
    });
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}
