<div class="container-fluid">
    <div class="row justify-content-between">
        <div class="col-md-3 align-self-center">
            <h5 class="text-primary font-weight-bold m-0" style="font-size: 18px;">Languages</h5>
        </div>
        <div class="col-md-9 align-self-center">
            <div class="row justify-content-end">
                <div *ngIf="updateIDs?.length" class="col-md-5 d-flex justify-content-end">
                    <button class="btn btn-primary add-button btn-sm py-0 text-white"
                        (click)="confirm('activate')">Activate</button>
                    <button class="btn btn-danger add-button btn-sm py-0 ml-3 text-white"
                        (click)="confirm('deactivate')">Deactivate</button>
                </div>
                <div class=" col-md-4 d-flex justify-content-end" aria-describedby="helperText">
                    <input class="form-control py-2" type="text" name="searchField" id="searchField"
                        placeholder="Search by Language Name" [(ngModel)]="searchText"
                        (keydown)="onInputChange($event)">
                    <small *ngIf="showHelperText" id="helperText" class="form-text text-muted pl-1">
                        Please Enter more than 2 characters to search
                    </small>
                </div>
                <div class="col-md-3 d-flex justify-content-end">
                    <button placement="bottom" ngbTooltip="Add Language" class="btn btn-primary text-white form-control add-button" (click)="add()"
                        type="submit"><i class="fas fa-plus d-inline fa-sm mr-1"></i><span class="custom-add">Add
                            Language</span></button>
                </div>
            </div>
        </div>
    </div>
    <div class="row form-group mt-4">
        <div class="col-md-12">
            <ngx-datatable class="bootstrap" [rows]="records" [columns]="columns" [loadingIndicator]="false"
                [limit]="limit" [offset]="offsetPaginate" [count]="totalCount" [externalPaging]="true"
                (page)="paginate($event)"
                [sorts]="[{prop: 'name', dir: 'asc'}, {prop: 'identifier', dir: 'asc'}, {prop: 'createdAt', dir: 'desc'}, {prop: 'updatedAt', dir: 'asc'},{prop: 'isDeleted', dir: 'asc'}]"
                [reorderable]="true" [externalSorting]="true" [columnMode]="ColumnMode.force" [headerHeight]="50"
                [footerHeight]="50" rowHeight="auto" (sort)="sort($event)" #dataTable>
            </ngx-datatable>
            <ng-template class="mx-2" #checkboxTemplate let-row="row" let-value="value">
                <form>
                    <input #checkBox name="check" type="checkbox" class="mx-2 myCheck" id={{value}}
                        (click)="checkRecords(checkBox, row)">
                </form>
            </ng-template>
            <ng-template #createdTemplate let-row="row" let-value="value">
                {{value | date :'longDate'}}
            </ng-template>
            <ng-template #updatedTemplate let-row="row" let-value="value">
                {{value | date :'longDate'}}
            </ng-template>
            <ng-template #headerTemplate let-column="column"> {{ column.name }}</ng-template>
            <ng-template #rowTemplate let-row="row" let-value="value">
                <i class="fas fa-edit cursor-pointer mr-4 ml-3" data-toggle="tooltip" data-placement="top" title="Edit"
                    (click)="edit(value)" aria-hidden="true"></i>
                <!-- <i class="fa fa-trash cursor-pointer ml-2" data-toggle="tooltip" data-placement="top" title="Delete"
                    (click)="confirm(value)"></i> -->
            </ng-template>
        </div>
    </div>
</div>