import { DatePipe } from '@angular/common';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators, AbstractControl } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { Title } from 'chart.js';
import * as moment from 'moment';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { ApproverServiceService } from 'src/app/carbon/services/approver-service.service';
import { CarboncurrencyService } from 'src/app/carbon/services/carboncurrency.service';
import { EmissionfactorserviceService } from 'src/app/carbon/services/emissionfactorservice.service';
import { EmissiontypeService } from 'src/app/carbon/services/emissiontype.service';
import { GeneralInfoService } from 'src/app/carbon/services/general-info.service';
import { LanguageTranslateService } from 'src/app/carbon/services/language-translate.service';
import { SidenavService } from 'src/app/carbon/services/sidenav.service';
import { CountryService } from 'src/app/shared/carbon-service/country.service';
import { AuthService } from 'src/app/shared/services/auth.service';
import { DataService } from 'src/app/shared/services/data.service';
import { CleaningGeneralInformationService } from '../../services/cleaning-general-information.service';
import { SeasiteService } from 'src/app/sea/services/seasite.service';

@Component({
  selector: 'app-general-information',
  templateUrl: './general-information.component.html',
  styleUrls: ['./general-information.component.scss']
})
export class GeneralInformationComponent {
  showMonth = false;
  month: number;
  submitted = false;

  @ViewChild('start') start!: ElementRef;
  @ViewChild('approved') approved!: ElementRef;
  selectedDatasource: any;
  minStartDate: any;
  maxStartDate: any;
  minEndDate: any;
  maxEndDate: any;
  endDateDisabled = true;
  startDate: any;
  endDate: any;
  generalInformation!: UntypedFormGroup;
  dataModel: any;
  countryList: any = [];
  revenueFood = { revenue: 0 };
  id: any;
  isSiteCreated = false;
  siteId: any;
  countryId: any;
  services: any[];
  servicesName: any[];
  servicesId: any[];
  subscription: Subscription = new Subscription();
  address: any;
  siteCreatedId: any;
  isChecked = false;
  dialogRef: any;
  dialogRefApproved: any;
  day: number;
  emissionFactList: any;
  config = {
    displayKey: 'value',
    search: true,
    placeholder: 'Choose currency',
    searchPlaceholder: 'Search currency'
  };
  currency: any[];
  monthString: any;
  dayString: string;
  isRoleAdminOrSuper: any;
  isAprrover = false;
  roleApprover: any;
  userEmail: any;
  managerRole: any;
  approvedEmail: string;
  language = this.service.languageIdentifier;
  cleaningRevenues: any = [];
  sidenavOpened: boolean = false;

  // tslint:disable-next-line:variable-name
  constructor(
    private fb: UntypedFormBuilder,
    public service: CleaningGeneralInformationService,
    private router: Router,
    private datePipe: DatePipe,
    private countryservice: CountryService,
    private toaster: ToastrService,
    private spinner: NgxSpinnerService,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private cus: CarboncurrencyService,
    private translate: TranslateService,
    private data: DataService,
    private lts: LanguageTranslateService,
    public oAuth: AuthService,
    private site: SeasiteService,
  ) {
    translate.use('en');
    translate.setTranslation('en', this.lts.state);
    const start = new Date(moment(new Date()).startOf('day').valueOf()).getTime();
    this.maxStartDate = new Date(moment(new Date()).startOf('day').valueOf());
    this.minStartDate = new Date(moment(start).subtract(1, 'year').endOf('day').valueOf());
    this.isSiteCreated = this.service.isSiteCreated;
    this.siteId = this.service.siteId;
  }
  ngOnInit(): void {
    this.generalInformation = this.fb.group({});
    // this.currency = this.service.currencyList;
    this.services = this.route.snapshot.data.service;
    this.approvedEmail = sessionStorage.getItem('email');
    this.getCurrency();
    this.getServices();
    this.createForm();
    this.getCountryList();
    this.spinner.show();
    this.oAuth.getRoleForCarbon().subscribe((res1: any) => {
      this.approvedEmail = res1.email;
      this.managerRole = res1.role;
      this.userEmail = res1.email;
      if (res1.role === 'Approver' || res1.role === 'SuperAdmin' || res1.role === 'SubAdmin') {
        this.isAprrover = true;
      }
      if (!this.oAuth.isRoleAdminOrSuper) {
        this.service.showCalculation = false;
      }
      if (this.isSiteCreated) {
        const res = this.service.generalInfo;        
        this.siteCreatedId = res.id;
        this.patchForm(res);
      } else {
        const res = this.service.siteInfo;
        this.patchForm(res);
      }
      this.spinner.hide();
    }, (err: any) => {
      this.toaster.error('Unable to Get user role', 'Try Again');
      this.spinner.hide();
    });
  }
  getServices() {
    this.cus.getServiceList().subscribe((res: any) => {
      this.cleaningRevenues = res.filter(ser => (ser.name == "Cleaning" && ser.isActive == true));
    }, (e) => {
      console.log('error', e);
    });
  }
  getCurrency() {
    this.cus.getList().subscribe((res: any) => {
      this.currency = res;
    }, (e) => {
      console.log('error', e);
    });
  }
  getCountryList() {
    const getList$ = this.countryservice.getList().subscribe((result: any) => {      
      this.countryList = result;
      if (this.countryId && this.countryId !== undefined) {
        const countryName = this.countryList.filter(e => e.id === +this.countryId);        
        this.generalInformation.patchValue({
          siteCountry: countryName[0]?.name || ''
        });
      }
    }, (err: any) => {
      this.toaster.error('unable to get country list', 'Error!');
    });
    this.subscription.add(getList$);
  }

  /**
   * @descritpion Patching the form from the site created data or from the site info
   * @param res This holds object
   * @returns void
   */
  patchForm(res): void {
    this.spinner.show();
    let revenues;
    try {
      revenues = JSON.parse(res.revenue);
    } catch (e) {
      revenues = res.revenue;
    }
    this.revenueFood.revenue = revenues?.revenue;
    const siteInfo = this.service.siteInfo;    
    let newstartdate = '';
    let newenddate = '';
    if (this.isSiteCreated) {
      if (res.startDate.toString() !== '' && res.endDate.toString() !== '') {
        const startdate = res.startDate;
        newstartdate = startdate.split('/').reverse().join('/');
        const enddate = res.endDate;
        newenddate = enddate.split('/').reverse().join('/');
        this.generalInformation.controls.startDate.disable();
        this.generalInformation.controls.endDate.disable();
        this.monthDiff(new Date(newstartdate), new Date(enddate));
      }
      this.countryId = res.country;
      if (siteInfo && Object.keys(siteInfo.services).length !== 0) {
        this.servicesName = (siteInfo.services).filter(e => e.id === 4);
        this.servicesId = siteInfo.services;
      } else {
        this.servicesName = [];
        this.servicesId = [];
      }
    } else {
      this.address = res.address;
      this.servicesName = (siteInfo.services || []).filter(e => e.id === 4);
      this.servicesId = siteInfo.services;
    }
    this.isChecked = res.ShowCalculation;    
    this.generalInformation.patchValue({
      client: siteInfo?.name,
      siteName: siteInfo?.siteId,
      siteAddress: (siteInfo?.address.building ? siteInfo?.address.building + ', ' : '') + (siteInfo?.address.street ? siteInfo?.address.street + ', ' : '') + (siteInfo?.address.state ? siteInfo?.address.state + ', ' : '') + (siteInfo?.country ? siteInfo?.country + ', ' : '') + (siteInfo?.address.zip ? siteInfo?.address.zip + '.' : ''),
      siteCountry: siteInfo?.country || null,
      zipcode: siteInfo?.address.zip || '',
      SurfaceAreaOfTheSite: res.surfaceArea ? res.surfaceArea : '',
      SurfaceOfCleanedArea: res.surfaceCleanedArea ? res.surfaceCleanedArea : '',
      NumberOfHoursSpentForCleaning: res.numberOfHourSpentCleaning ? res.numberOfHourSpentCleaning : '',
      CleaningRevenue: res && res.cleaningRevenue ? res.cleaningRevenue.id : '',
      NoofPeopleSodexo: res.noPeopleSodexo ? res.noPeopleSodexo : '',
      services: this.servicesName[0]?.language[this.language || 'en'],
      startDate: newstartdate === '' ? '' : this.datePipe.transform(newstartdate, 'yyyy-MM-dd'),
      endDate: newenddate === '' ? '' : this.datePipe.transform(newenddate, 'yyyy-MM-dd'),
      currency: this.siteCreatedId ? res.currency.id : ''
    });
    this.spinner.hide();
  }
  /**
   * @descritpion Enable the toDate picker by from date selected
   * @param e datepicker date value
   * @returns void
   */

  dateStartChange(e: any) {
    this.endDateDisabled = false;
    this.maxEndDate = new Date(moment(this.generalInformation.value.startDate).add(365, 'days').startOf('day').valueOf());
    // this.minEndDate = new Date(moment(this.generalInformation.value.startDate).add(1, 'month').startOf('day').valueOf());
    this.minEndDate = new Date(moment(this.generalInformation.value.startDate).startOf('day').valueOf());
    // this.getMonthsDiff();
    this.monthDiff(new Date(this.generalInformation.value.startDate), new Date(this.generalInformation.value.endDate));
  }
  /**
   * @descritpion To calculate the month difference and patch in the form of month control
   * @returns void
   */
  getMonthsDiff() {
    if (this.generalInformation.value.startDate !== '' && this.generalInformation.value.endDate !== '') {
      let months;
      months = (this.generalInformation.value.endDate._d.getFullYear() - this.generalInformation.value.startDate._d.getFullYear()) * 12;
      months -= this.generalInformation.value.startDate._d.getMonth();
      months += this.generalInformation.value.endDate._d.getMonth();
      const monthsvalue = months <= 0 ? 0 : months;
      this.generalInformation.patchValue({
        month: monthsvalue
      });
    }
  }
  /**
   * @descritpion creating a form for general information of carbon site data
   * @returns void
   */
  createForm() {
    this.generalInformation = this.fb.group({
      client: [''],
      siteName: [''],
      siteAddress: [''],
      siteCountry: [''],
      zipcode: [''],
      services: [''],
      SurfaceAreaOfTheSite: ['', [Validators.required, Validators.min(1)]],
      SurfaceOfCleanedArea: ['', [Validators.required, Validators.min(1)]],
      NoofPeopleSodexo: ['', [Validators.required, Validators.min(1)]],
      NumberOfHoursSpentForCleaning: ['', [Validators.required, Validators.min(1)]],
      currency: ['', Validators.required],
      CleaningRevenue: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
      month: ['']
    });
  }
  /**
   * @descritpion Submitting form
   * @returns void
   */
  formSubmit() {
    this.submitted = true;
    this.generalInformation.patchValue({
      startDate: new Date(this.generalInformation.value.startDate),
      endDate: new Date(this.generalInformation.value.endDate)
    });
    const payload = {
      startDateTimeStrap: new Date(this.generalInformation.value.startDate).getTime(),
      endDateTimeStrap: new Date(this.generalInformation.value.endDate).getTime()
    };
  }
  /**
   * @descritpion Returning the form controls from the form group
   * @returns formControls
   */
  get f(): { [key: string]: AbstractControl; } {
    return this.generalInformation.controls;
  }

  /**
   * @descritpion Constructing payload and Request to API, if Already created its updated or creating new entry
   * @returns void
   */
  submit() {
    this.submitted = true;
    if (this.generalInformation.valid) {
      this.spinner.show();
      const countryId = this.countryList.filter(e => e.name === this.generalInformation.value.siteCountry);
      if (!this.oAuth.isRoleAdminOrSuper) {
        this.isChecked = false;
      }
      const payload = {
        clientName: this.generalInformation.value.client,
        clientId: this.generalInformation.value.siteName,
        address: this.generalInformation.value.siteAddress,
        country: countryId[0]?.id,
        zipCode: this.generalInformation.value.zipcode,
        siteId: this.siteId,
        seatype: this.service?.siteInfo?.seaType,
        startDate: moment(this.generalInformation.controls.startDate.value).format('YYYY-MM-DD'),
        endDate: moment(this.generalInformation.controls.endDate.value).format('YYYY-MM-DD'),
        service: this.servicesId,
        ShowCalculation: this.isChecked,
        currency: this.generalInformation.value.currency,
        surfaceArea: this.generalInformation.value.SurfaceAreaOfTheSite,
        surfaceCleanedArea: this.generalInformation.value.SurfaceOfCleanedArea,
        noPeopleSodexo: this.generalInformation.value.NoofPeopleSodexo,
        numberOfHourSpentCleaning: this.generalInformation.value.NumberOfHoursSpentForCleaning,
        cleaningRevenue: this.generalInformation.value.CleaningRevenue
      };
      if (!this.isSiteCreated) {
        const payload$ = this.service.create(payload).subscribe((res: any) => {
          this.service.showCalculation = res.ShowCalculation;
          this.service.isSiteCreated = true;
          this.goToNext();
          this.submitted = false;
          this.spinner.hide();
          this.toaster.success('Site saved successfully');
        },
          err => {
            this.submitted = false;
            this.spinner.hide();
            this.toaster.error('unable to save site Informations', 'Error!');
          });
        this.subscription.add(payload$);
      } else {
        const payload$ = this.service.update(payload, this.siteCreatedId).subscribe((res: any) => {
          this.service.generalInfo = res;
          this.service.showCalculation = res.ShowCalculation;
          this.data.updateReload(true);
          this.goToNext();
          this.submitted = false;
          this.spinner.hide();
          this.toaster.success('Site updated successfully');
        },
          err => {
            this.submitted = false;
            this.spinner.hide();
            this.toaster.error('unable to update site Informations', 'Error!');
          });
        this.subscription.add(payload$);
      }
    }
  }
  /**
   * @descritpion Opening a dialog for asking confirmation
   * @param template Is passed from the VIEW throught Template Reference Variable
   * @returns void
   */
  resetSite(template) {
    this.dialogRef = this.modalService.open(template, { centered: true });
  }
  /**
   * @descritpion This function manage to show date in VIEW with help of @see dateDiff()
   * @param d1 from date
   * @param d2 to date
   * @returns void
   */
  monthDiff(d1, d2) {
    const date = this.dateDiff(d1, d2);
    const months = date.monthDiff || 0;
    const diffDays = date.dayDiff || 0;
    if (diffDays) {
      this.day = diffDays <= 0 ? 0 : diffDays;
    } else {
      this.day = 0;
    }
    this.dayString = this.day === 0 || this.day === 1 ? 'kDay' : 'kDays';
    // tslint:disable-next-line:triple-equals// tslint:disable-next-line:use-isnan
    if (months) {
      this.month = months <= 0 ? 0 : months;
    } else {
      this.month = 0;
    }
    this.monthString = this.month === 0 || this.month === 1 ? 'kMonth' : 'kMonths';
    this.showMonth = true;
  }
  /**
   * @description converting string to number using parseInt js function
   * @param dataString is passing as string
   * @returns number
   */
  convertIntoNum(dataString: string): number {
    // tslint:disable-next-line:radix
    return parseInt(dataString);
  }
  /**
   * @description This function navigate to the Next module when submitting a Data
   * @returns void
   */
  goToNext() {
    this.router.navigate([`carbon-cleaning/${this.service.siteId}/employeecommuting`])
      .then(() => {
        if (!this.siteCreatedId) {
          // window.location.reload();
        }
      });
  }
  /**
   * @description Closes the dialog
   * @returns void
   */
  close() {
    this.dialogRef.close();
  }
  /**
   * @description This function helps to close the dialog for open reset confirmation dialog and
   * calls the API to soft delete the SITE data
   * @returns void
   */
  confirmReset() {
    this.close();
    
    this.service.getDeleteAndReset(this.siteCreatedId, this.siteId).subscribe((res: any) => {
      this.toaster.success(res.message, 'Reset');
      // this.service.isAprroved();
      window.location.reload();
    }, (err: any) => {
      this.toaster.error('unable to reset information', 'Error');
    });
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  /**
   * @description This function helps to open template for the view more option
   * @param tem This holds the content
   * @returns void
   */
  openViewMore(tem) {
    this.dialogRef = this.modalService.open(tem, { centered: true });
  }
  /**
   * @description This function calculate the ton calculation for the revenue
   * @returns void
   */
  calculateTco2e() {
    const foodvalue = this.generalInformation.value.foodService;
    this.revenueFood.revenue = foodvalue;
  }
  /**
   * @description This function accepts two argument for calcuting the months and days in-between
   * @param startingDate This holds start date
   * @param endingDate This holds end date
   * @returns object as a property of monthDiff, dayDiff
   */
  dateDiff(startingDate: Date, endingDate: Date): any {
    let startDate = new Date(startingDate);
    // user not pass endingDate then set current date as end date.
    if (!endingDate) {
      endingDate = new Date();
    }
    let endDate = new Date(endingDate);
    // chack start date and end date and base on condication alter date.
    if (startDate > endDate) {
      const swap = startDate;
      startDate = endDate;
      endDate = swap;
    }
    // This is for leap year.
    const startYear = startDate.getFullYear();
    const february =
      (startYear % 4 === 0 && startYear % 100 !== 0) || startYear % 400 === 0
        ? 29
        : 28;
    const daysInMonth = [31, february, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    let yearDiff = endDate.getFullYear() - startYear;
    let monthDiff = endDate.getMonth() - startDate.getMonth();
    if (monthDiff < 0) {
      yearDiff--;
      monthDiff += 12;
    }
    let dayDiff = endDate.getDate() - startDate.getDate();
    if (dayDiff < 0) {
      if (monthDiff > 0) {
        monthDiff--;
      } else {
        yearDiff--;
        monthDiff = 11;
      }
      dayDiff += daysInMonth[startDate.getMonth()];
    }
    if (monthDiff === 0 && yearDiff !== 0) {
      monthDiff = 12;
    }
    if (daysInMonth[startDate.getMonth()] === endDate.getDate()) {
      monthDiff++;
      dayDiff = 0;
    }
    return {
      monthDiff,
      dayDiff
    };
  }
  isApprovedPopup() {
    this.dialogRefApproved = this.modalService.open(this.approved, {
      centered: true,
    });
  }
  isAprroved() {
    this.roleApprover = this.service.siteInfo;
    const payload = {
      approved: {
        siteId: this.roleApprover.id,
        approved: 1,
        approvedBy: this.approvedEmail,
        updatedBy: this.roleApprover.updatedBy,
        approvedDate: moment(this.roleApprover.updatedAt).format('DD/MM/YYYY'),
      }
      // approvedEmail: this.userEmail
    };
    // this.apService.updateApprover(payload, this.roleApprover.id).subscribe((data: any) => {
    //     this.service.updateApproverInfo(payload.approved);
    //     this.closeisAprroved();
    //     this.toaster.success('site approved');
    //     window.location.reload();
    //   });
  }
  closeisAprroved() {
    this.dialogRefApproved.close();
  }
  toggleSidenav() {
    this.sidenavOpened = !this.sidenavOpened;
    this.site.setSidenavState(this.sidenavOpened);
  }
}
