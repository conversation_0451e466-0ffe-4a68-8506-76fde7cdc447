<div>
    <div class="col-md-12 mt-4 d-flex justify-content-between">
        <div class="col-md-3 align-self-center">
            <h5 class="text-primary font-weight-bold m-0" style="font-size: 18px;">SEA Store List</h5>
        </div>
        <div class="col-md-9 align-self-center">
            <div class="row justify-content-end">
                <div *ngIf="updateIDs.length" class="col-md-5 d-flex justify-content-end">
                    <button class="btn btn-primary add-button btn-sm py-0 text-white"
                        (click)="confirm('activate')">Activate</button>
                    <button class="btn btn-danger add-button btn-sm py-0 ml-3 text-white"
                        (click)="confirm('deactivate')">Deactivate</button>
                </div>
                <div class="col-md-4 d-flex justify-content-end" aria-describedby="helperText">
                    <input class="form-control py-2" type="text" name="searchField" id="searchField"
                placeholder="Search by Initiative Name" [(ngModel)]="searchText" (input)="onInputChange($event)">
                </div>
                <div class="col-md-3 d-flex justify-content-end">
                    <button class="btn btn-primary" ngbTooltip="Add Initiative" (click)="openInitiative(false, '')">   
                        <span class="custom-add">Add Initiative</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12 mt-4">
        <ngx-datatable class="bootstrap cursor-pointer" [rows]="initiativeData" [columnMode]="ColumnMode.force"
            [headerHeight]="headerHeight" [footerHeight]="footerHeight" [rowHeight]="rowHeight" [limit]="limit"
            [sorts]="[{prop: 'name', dir: 'asc'}]" [offset]="offsetPaginate" [count]="count" [externalPaging]="false"
            style=" height: 100%;width: 100%;">

            <ngx-datatable-column [width]="30" [sortable]="false" [canAutoResize]="false" [draggable]="false"
                [resizeable]="false" >
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <input type="checkbox" 
                           name="check"
                          (change)="CheckRecords($event.target, row)" 
                          [checked]="updateIDs.includes(row.initiativeid)" />
                </ng-template>
            </ngx-datatable-column>

            <ngx-datatable-column name="Initiative Id" prop="initiativeid" [minWidth]="100">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Initiative Name" prop="initiativename" [minWidth]="170">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Country" prop="country" [minWidth]="100">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Address" prop="address" [minWidth]="100">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Contact" prop="contact" [minWidth]="150">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        'SiteManager'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Investment" prop="investment" [minWidth]="75">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Saving" prop="saving" [minWidth]="150">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <!-- <ngx-datatable-column name="Time" prop="time">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column> -->
            <ngx-datatable-column name="Period" prop="period" [minWidth]="100">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Score" prop="score" [minWidth]="50">
                <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                        '-'}}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Status" prop="isActive" [minWidth]="70">
                <ng-template let-value="value" ngx-datatable-cell-template> 
                    <span>{{ value === 1 ? 'Active' : (value === 0 ? 'InActive' : 'Active') }}</span>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Action" prop="Action" [minWidth]="50" [maxWidth]="500">
                <ng-template let-row="row" ngx-datatable-cell-template>
                    <mat-icon aria-hidden="false" (click)="openInitiative(true, row.id)">edit</mat-icon>
                </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column name="Delete" prop="status" [minWidth]="70" headerClass="text-left" cellClass="text-left ps-1">
                <ng-template let-value="value" let-row="row" ngx-datatable-cell-template>
                    <mat-icon aria-hidden="false" color="warn" (click)="deleteData(row)">delete_forever</mat-icon>
                </ng-template>
            </ngx-datatable-column>
        </ngx-datatable>
    </div>
</div>