import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { ExpertsRoutingModule } from './experts-routing.module';
import { ListComponent } from './list/list.component';
import { UpsertComponent } from './upsert/upsert.component';


@NgModule({
  declarations: [ListComponent, UpsertComponent],
  imports: [
    CommonModule,
    ExpertsRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    NgbModule,
    NgxDatatableModule
  ]
})
export class ExpertsModule { }
