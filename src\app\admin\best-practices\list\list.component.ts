import { BestPracticeService } from './../../../shared/services/best-practices.service';
import { HttpResponse } from '@angular/common/http';
import { Component, OnInit, TemplateRef, ViewChild, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { NgxSpinnerService } from 'ngx-spinner';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import { ColumnMode, DatatableComponent } from '@swimlane/ngx-datatable';
import { ListViewInterface, Sort } from 'src/app/shared/interface/list-view';
import { Expert } from 'src/app/shared/models/expert.model';
import { DataService } from 'src/app/shared/services/data.service';
import { ConfirmationWindowComponent } from 'src/app/shared/components/confirmation-window/confirmation-window.component';
import { ConstantData } from 'src/app/shared/constants/constant';
import { ExpertService } from 'src/app/shared/services/expert.service';
import { CategoryService } from 'src/app/shared/services/category.service';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss']
})
export class ListComponent implements OnInit, ListViewInterface, OnDestroy {
  experts: Expert[];
  limit = 10;
  offset = 0;
  offsetPaginate = 0;
  page = 1;
  updateIDs = [];
  InActivateData = [];
  activateData = [];
  statusActive: boolean;
  statusInActive: boolean;
  totalCount: number;
  willDownload = false;
  bulkData = [];
  invalidRecords: any;
  validRecords: any;
  bulkPayload = [];
  topicsList: any;
  subscription: Subscription = new Subscription();
  columns = [];
  ColumnMode = ColumnMode;
  modalRef: NgbModalRef;
  sortBy: Sort = { name: 'name', value: 'ASC' };
  searchText = '';
  status: any;
  showHelperText = false;
  deBouncedInputValue = this.searchText;
  searchDebounce: Subject<string> = new Subject();
  people: Subject<string> = new Subject();
  importForm: UntypedFormGroup;
  fileName: any;


  @ViewChild('checkboxTemplate', { static: true }) checkboxTemplate: TemplateRef<any>;
  @ViewChild('createdTmpl', { static: true }) createdTmpl: TemplateRef<any>;
  @ViewChild('updatedTmpl', { static: true }) updatedTmpl: TemplateRef<any>;
  @ViewChild('editTmpl', { static: true }) editTmpl: TemplateRef<any>;
  @ViewChild('hdrTpl', { static: true }) hdrTpl: TemplateRef<any>;
  @ViewChild('dataTable', { static: true }) table: DatatableComponent;
  practiceList: any;
  isSubAdmin = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: UntypedFormBuilder,
    private expertService: ExpertService,
    private ps: BestPracticeService,
    private spinner: NgxSpinnerService,
    private modalService: NgbModal,
    private toaster: ToastrService,
    private topicService: CategoryService,
    private dataService: DataService,
    private ts: Title) {
    this.ts.setTitle('SEA Admin - Best Practices');

    const datatable$ = this.dataService.resizeDataTable.subscribe(data => {
      if (this.table) {
        this.resizeDataTable();
      }
    });
    this.subscription.add(datatable$);
  }

  ngOnInit() {
    this.createForm();
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.isSubAdmin = true;
    }
    this.columns = [
      {
        cellTemplate: this.checkboxTemplate,
        name: '',
        prop: 'id',
        sortable: false,
        minWidth: 75,
        maxWidth: 100
      },
      { prop: 'bestPracticeId', name: 'Best Practice Id', minWidth: 130,
        maxWidth: 300, sortable: true },
      { prop: 'name', name: 'Name', minWidth: 190,
        maxWidth: 300, sortable: true },
      { prop: 'score', name: 'Score', minWidth: 100,
        maxWidth: 300, sortable: false },
      { prop: 'country', name: 'Country', minWidth: 130,
        maxWidth: 300, sortable: false },
      { prop: 'topicName', name: 'Topic', minWidth: 130,
        maxWidth: 300, sortable: false },
      // { prop: 'imageUrl', name: 'ImageUrl', width: 200, sortable: false },
      {
        name: 'Status',
        prop: 'isActive',
        sortable: true,
        minWidth: 100,
        maxWidth: 300,
      },
      {
        cellTemplate: this.createdTmpl,
        name: 'Created At',
        prop: 'createdAt',
        sortable: true,
        minWidth: 130,
        maxWidth: 300,
      },
      {
        cellTemplate: this.updatedTmpl,
        name: 'Updated At',
        prop: 'updatedAt',
        minWidth: 130,
        maxWidth: 300,
        sortable: true
      },
      {
        cellTemplate: this.editTmpl,
        headerTemplate: this.hdrTpl,
        name: 'Action',
        prop: 'id',
        sortable: false,
        minWidth: 130,
        maxWidth: 300,
      }
    ];
    // this.spinner.show();
    // this.practiceList = this.experts;
    // this.spinner.hide();

    const params$ = this.route.queryParams.subscribe((res) => {
      if (Object.keys(res).length) {
        this.searchText = res.search ? res.search : '';
        this.limit = res.limit ? Number(res.limit) : 10;
        this.offset = res.offset ? Number(res.offset) : 0;
        this.sortBy.name = res.sortName ? res.sortName : 'name';
        this.sortBy.value = res.sortValue ? res.sortValue : 'ASC';
        this.getDataSource();
      } else {
        this.getDataSource();
      }
    });
    this.subscription.add(params$);
    this.setupSearchDeBouncer();
    this.offsetPaginate = this.offset / this.limit;
  }

  createForm() {
    this.importForm = this.fb.group({
      bulkImport: ''
    });
  }

  // Common Service
  getDataSource(offset?) {
    this.spinner.show();
    let search;
    const countryCondition = this.isSubAdmin ? sessionStorage.getItem('country') : '';
    if (this.isSubAdmin) {
      this.searchText.length ?
        search = `&where=` + JSON.stringify({ name: { contains: this.searchText }, country: countryCondition }) : search = '';
    } else {
      this.searchText.length ? search = `&where=` + JSON.stringify({ name: { contains: this.searchText } }) : search = '';
    }
    const segments$ = this.ps.getRecord(this.limit, offset != undefined ? offset : this.offset, this.sortBy, search, countryCondition ? `country=${countryCondition}` : '')
      .subscribe((res: HttpResponse<any>) => {
        this.spinner.hide();
        this.totalCount = parseInt(res.headers.get('content-count'), 10);
        this.experts = res.body;
        this.spinner.show();
        this.practiceList = this.experts;
        this.spinner.hide();

        // Adding Nil(-) part
        this.experts.map((item: any) => {
          if (item.topic == null) {
            item.topicName = '-';
            return item;
          } else {
            item.topicName = item.topic.name;
            return item;
          }
        });

        this.experts.map((item: any) => {
          if (item.isActive) {
            item.isActive = 'Active';
            this.activateData.push(item.id);
          } else {
            item.isActive = 'Inactive';
            this.InActivateData.push(item.id);
          }
        });
        this.experts = this.experts.map((item, i) => {
          item.index = i + 1 + this.offset;
          return item;
        });
      }, error => {
        this.spinner.hide();
        throw error;
      });
    this.subscription.add(segments$);
  }

  paginate(event) {
    this.limit = event.limit;
    this.offset = this.limit * event.offset;
    this.offsetPaginate = event.offset;
    this.router.navigate(['admin/bestpractice'], {
      queryParams: {
        limit: this.limit,
        offset: this.offset,
        sortName: this.sortBy.name,
        sortValue: this.sortBy.value,
        search: this.searchText,
      }
    });
    const checkBox = document.getElementsByName('check');
    checkBox.forEach((item: HTMLInputElement) => item.checked = false);
    this.updateIDs = [];
    this.activateData = [];
    this.InActivateData = [];
  }

  sort(event) {
    if (this.totalCount > 1) {
      this.sortBy.name = event.column.prop;
      this.sortBy.value = event.newValue;
      this.router.navigate(['admin/bestpractice'], {
        queryParams: {
          limit: this.limit,
          offset: this.offset,
          sortName: this.sortBy.name,
          sortValue: this.sortBy.value,
          search: this.searchText,
        }
      });
    }
  }

  add() {
    this.router.navigate(['admin/bestpractice/add']);
  }

  edit(id: number) {
    this.router.navigate([`admin/bestpractice/edit/${id}`], {
      queryParams: {
        offset: this.offset,
      }
    });
  }

  remove(id: number) {
    this.spinner.show();
    const segment$ = this.ps.removeRecord(id).subscribe((res: any) => {
      this.spinner.hide();
      const index = this.experts.findIndex(item => item.id === id);
      this.experts.splice(index, 1);
      if (this.experts.length) {
        this.experts = [...this.experts];
        this.experts = this.experts.map((item, i) => {
          item.index = i + 1 + this.offset;
          return item;
        });
      } else {
        this.offset = this.offset - 10;
        this.getDataSource();
      }
      this.toaster.success('Expert deleted successfully');
    }, error => {
      this.spinner.hide();
      throw error;
    });
    this.subscription.add(segment$);
  }


  confirm(props) {
    this.statusActive = this.activateData.some(item => this.updateIDs.includes(item));
    this.statusInActive = this.InActivateData.some(item => this.updateIDs.includes(item));
    if (props === 'activate' && !this.statusActive) {
      this.callModal(props);
    } else if (props === 'deactivate' && !this.statusInActive) {
      this.callModal(props);
    } else if (props === 'deactivate' && (this.statusInActive && this.statusActive)) {
      this.callModal(props);
    } else if (props === 'activate' && (this.statusInActive && this.statusActive)) {
      this.callModal(props);
    } else {
      // this.changeDetectorRef.detectChanges();
      this.toaster.warning(`${this.updateIDs.length > 1 ? 'Experts' : 'Expert'} Already ${props}d `);
      const checkBox = document.getElementsByName('check');
      checkBox.forEach((item: HTMLInputElement) => item.checked = false);
      this.updateIDs = [];
      // const deleteModal = this.modalService.open(ConfirmationWindowComponent);
      // deleteModal.componentInstance.title = ConstantData.deleteInfo.title;
      // deleteModal.componentInstance.content = ConstantData.deleteInfo.content;
      // deleteModal.componentInstance.recordId = props;
      // deleteModal.result.then(() => {
      //   this.remove(props);
      // }, (data) => { });
    }
  }

  callModal(props) {
    if (props === 'activate') {
      const activateModal = this.modalService.open(ConfirmationWindowComponent);
      activateModal.componentInstance.title = ConstantData.ActivateInfo.title;
      activateModal.componentInstance.content = ConstantData.ActivateInfo.content;
      activateModal.componentInstance.updateState = props;
      activateModal.result.then((res: any) => {
        this.updateStatus(res);
      }, (data) => { });

    } else if (props === 'deactivate') {
      const deactivateModal = this.modalService.open(ConfirmationWindowComponent);
      deactivateModal.componentInstance.title = ConstantData.DeactivateInfo.title;
      deactivateModal.componentInstance.content = ConstantData.DeactivateInfo.content;
      deactivateModal.componentInstance.updateState = props;
      deactivateModal.result.then((res: any) => {
        this.updateStatus(res);
      }, (data) => { });

    } else {
      return false;
    }
  }

  // Bulk Update Selection
  CheckRecords(template, val) {
    if (template.checked) {
      this.updateIDs.push(val.id);
    } else {
      const index = this.updateIDs.findIndex(item => item === val.id);
      this.updateIDs.splice(index, 1);
    }
  }

  // Bulk Update Selection Logic
  updateStatus(updateStatus) {
    let selectedId;
    if (updateStatus === 'activate') {
      selectedId = this.updateIDs.filter(i => !this.experts.filter(item => item.isActive === 'Active').map(itm => itm.id).includes(i));
    } else if (updateStatus === 'deactivate') {
      selectedId = this.updateIDs.filter(i => !this.experts.filter(item => item.isActive === 'Inactive').map(itm => itm.id).includes(i));
    }
    if (selectedId.length) {
      const payload = selectedId.map((item) => {
        return {
          id: item,
          isActive: updateStatus === 'activate' ? true : false,
        };
      });
      const segmentStatus$ = this.ps.updateStatus(payload).subscribe((res) => {
        this.toaster.success(`${selectedId.length > 1 ? 'Best Practices' : 'Best Practices'} ${updateStatus === 'deactivate' ? 'Deactivated' : 'Activated'}  Successfully`);
        const checkBox = document.getElementsByName('check');
        checkBox.forEach((item: HTMLInputElement) => item.checked = false);
        this.updateIDs = [];
        this.activateData = [];
        this.InActivateData = [];
        this.getDataSource();
      }, error => {
        throw error;
      });
      this.subscription.add(segmentStatus$);
    }
  }

  onInputChange(e) {
    this.offsetPaginate = 0;
    this.offset = this.limit * this.offset;
    const searchText = e.target.value;
    if (!!searchText) {
      this.searchDebounce.next(searchText);
    }
  }

  setupSearchDeBouncer() {
    const search$ = this.searchDebounce.pipe(
      debounceTime(350),
      distinctUntilChanged(),
    ).subscribe((term: string) => {
      this.deBouncedInputValue = term;
      const offset = 0;
      this.getDataSource(offset);
    });
    this.subscription.add(search$);
  }

  resizeDataTable() {
    this.experts = this.experts.map((item, i) => {
      item.index = i + 1 + this.offset;
      return item;
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}
