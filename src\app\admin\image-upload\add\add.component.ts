import { ChangeDetectorRef, Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { ImageuploadService } from 'src/app/shared/services/imageupload.service';

@Component({
  selector: 'app-add',
  templateUrl: './add.component.html',
  styleUrls: ['./add.component.scss']
})
export class AddComponent implements OnInit {
  @ViewChild('fileInput') el: ElementRef;
  imageUrl: any = 'https://i.pinimg.com/236x/d6/27/d9/d627d9cda385317de4812a4f7bd922e9--man--iron-man.jpg';
  ImageForm: UntypedFormGroup;
  type: string;
  topicForm: any;
  toggleName: boolean;
  badgeurl: any;
  imageurl: any;
  images: { name: string; url: string; }[];
  dialogRef: any;
  submitted = false;
  datas: any;
  chooseFile: any;
  imageName: any = [];
  act: any;
  position: any;
  active: any;
  image: any;
  edited = false;
  breadCumsData: any;
  pageOffset: any;
  id: any;
  constructor(
    public fb: UntypedFormBuilder,
    private toaster: ToastrService,
    private route: ActivatedRoute,
    private upi: ImageuploadService,
    private router: Router,
    private spinner: NgxSpinnerService,
  ) { }

  ngOnInit(): void {
    this.createForm();

    this.pageOffset = this.route.snapshot.queryParams.offset;
    this.breadCumsData = {
      limit: 10,
      offset: this.pageOffset,
      search: '',
      sortName: 'createdAt',
      sortValue: 'desc'
    };
    if (this.route.snapshot.params.id) {
      this.toggleName = true;
      this.id = this.route.snapshot.params.id;
      this.patchForm();
    }
  }

  createForm() {
    this.ImageForm = this.fb.group({
      position: ['', Validators.required],
      active: ['', Validators.required],
    });
  }

  // validate part
  get f() {
    return this.ImageForm.controls;
  }
  create() {
    this.submitted = true;
    if (this.ImageForm.invalid) {
      return;
    } else {
      this.spinner.show();
      const payload = {
        row: this.ImageForm.value.position,
        activate: this.ImageForm.value.active
      };
      if (this.id) {
        this.upi.updateRecord(payload, this.id).subscribe((res) => {
          this.spinner.hide();
          this.toaster.success('image updated successfully');
          this.listRecord();
        }, error => {
          this.spinner.hide();
          throw error;
        });
      } else {
        this.uploadAttachFile(payload);
      }
    }
  }

  patchForm() {
    this.spinner.show();
    this.upi.getImageRecord(this.id).subscribe((res: any) => {
      this.ImageForm.patchValue({
        position: res.position,
        // imageUrl: res.fileName,
        active: res.activate,
      });
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      this.listRecord();
      throw error;
    });
  }
  fileUpload(event: any) {
    this.datas = event;
    this.chooseFile = event.target.files[0];
    this.imageName.push(event.target.files[0]);
  }

  uploadAttachFile(payload?: any) {
    if (this.datas) {
      const formData: FormData = new FormData();
      formData.append('file', this.chooseFile);
      this.upi.createimagesRecords(formData, payload.row, payload.activate).subscribe((result: any) => {
        if (result) {
          this.toaster.success('You have uploaded a file successfully');
          this.listRecord();
        }
      });
    } else {
    }
  }
  listRecord() {
    this.spinner.hide();
    this.router.navigate(['admin/imageupload'], {
      queryParams: this.breadCumsData
    });
  }
}
