<div *ngIf="sitesDataLoaded"> 
    <form #manager="ngForm" [formGroup]="editForm" (ngSubmit)="update()">
        <nav aria-label="breadcrumb">
            <div class="row form-group">
                <div class="col-md-6">
                    <ol class="breadcrumb bg-transparent  py-0 px-3">
                        <li class="breadcrumb-item">
                            <a class="cursor-pointer" [routerLink]="['/admin/managers']" [queryParams]="breadCumsData"> Site
                                Managers</a>
                        </li>
                        <li class="breadcrumb-item active">Edit</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <div class="row justify-content-end">
                        <!-- <div class="col-md-3" ngxPermissionsOnly="Admin">
                            <div class="checkbox">
                                <label class="text-primary"><input type="checkbox" value="" (click)="roleChange()"
                                        [checked]="showRole"> Make as Admin</label>
                            </div>
                        </div> -->
                        <!-- <div class="col-md-3">
                            <label for="active" class="align-bottom pr-2 m-0">Active</label>
                            <span>
                                 <mat-slide-toggle checked formControlName="active" id="active" size="small" color='rgb(42,41,92)'>
                                </mat-slide-toggle> 
                            </span>
                        </div> -->
                        <div class="col-md-12">
                            <div class="row form-group">
                                <div class="col-md-12 form-group text-right">
                                    <button type="reset" (click)="listRecord()" class="btn btn-danger mr-2">Cancel</button>
                                    <button type="submit" class="btn btn-primary text-right">Update</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    
        </nav>
    
        <div class="col-md-12">
            <mat-accordion displayMode="" multi class="mat-table" is-open="true">
                <mat-expansion-panel class="mt-4" expanded="true">
                    <mat-expansion-panel-header class="mat-row p-3 background">
                        <div class="d-flex custom-topic-size">
                            Key Informations
                        </div>
                    </mat-expansion-panel-header>
                    <mat-accordion>
                        <div class="mt-3">
                            <div class="col-md-12 text-left d-flex justify-content-end">
                                <label for="active" class="align-bottom pr-2 m-0">Active</label>
                                <span>
                                    <mat-slide-toggle checked formControlName="active" id="active" size="small"
                                        color='rgb(42,41,92)'>
                                    </mat-slide-toggle>
                                </span>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-6">
                                     <label class="custom-subtopic-size" class="custom-subtopic-size" for="firstName"><sup>*</sup>First Name</label>
                                    <input type="text" class="form-control" formControlName="firstName"
                                        placeholder="Enter First Name"
                                        [ngClass]="{ 'is-invalid': submitted && f.firstName.errors }" id="firstName">
                                    <span class="icon-position far fa-user"
                                        [ngClass]="{ 'icon-error ': submitted && f.firstName.errors }"></span>
                                    <div *ngIf="submitted && f.firstName.errors" class="invalid-feedback">
                                        <p *ngIf="f.firstName.errors.required">First Name is required</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                     <label class="custom-subtopic-size" for="lastName"><sup>*</sup>Last Name</label>
                                    <input type="text" class="form-control" formControlName="lastName"
                                        placeholder="Enter Last Name"
                                        [ngClass]="{ 'is-invalid': submitted && f.lastName.errors }" id="lastName">
                                    <span class="icon-position far fa-user"
                                        [ngClass]="{ 'icon-error ': submitted && f.lastName.errors }"></span>
                                    <div *ngIf="submitted && f.lastName.errors" class="invalid-feedback">
                                        <p *ngIf="f.lastName.errors.required">Last Name is required</p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-6">
                                     <label class="custom-subtopic-size" for="email"> <sup>*</sup>Manager Email</label>
                                    <input type="email" class="form-control" formControlName="email"
                                        placeholder="Enter Manager Email"
                                        [ngClass]="{ 'is-invalid': submitted && f.email.errors }" id="email"
                                        (ngModelChange)="emailChanged()">
                                    <span class="icon-position far fa-envelope"
                                        [ngClass]="{ 'icon-error ': submitted && f.email.errors }"></span>
                                    <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                                        <p *ngIf="f.email.errors.required"><sup>*</sup>Manager Email is required</p>
                                        <p *ngIf="f.email.errors.pattern">Email must be a valid mail address</p>
                                    </div>
                                </div>
                                <div class="col-md-6" id="id1">
                                     <label class="custom-subtopic-size" for="language">Language</label>
                                    <input id="language" placeholder="Search for language" type="text" class="form-control"
                                        formControlName="language" [ngbTypeahead]="searchLanguage"
                                        [resultFormatter]="resultFormatBandListLanguage"
                                        [inputFormatter]="inputFormatBandListLanguage"
                                        (focus)="focusLang$.next($event.target.value)"
                                        (click)="clickLang$.next($event.target.value)"
                                        [ngClass]="{ 'is-invalid': submitted && f.language.errors }"
                                        #instanceLang="ngbTypeahead" />
                                    <div *ngIf="submitted && f.language.errors" class="invalid-feedback">
                                        <div *ngIf="f.language.errors.required">Language is required</div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-6" id="id1">
                                     <label class="custom-subtopic-size" for="site">Site</label>
                                    <ngx-select-dropdown formControlName="site" [multiple]="true" [config]="siteConfig"
                                        [options]="sites">
                                    </ngx-select-dropdown>
                                </div>
                                <div class="col-md-6">
                                     <label class="custom-subtopic-size" for="phone">Manager Phone</label>
                                    <input type="number" class="form-control" formControlName="phone"
                                        placeholder="Enter Manager Phone" id="phone">
                                    <span class="icon-position fas fa-phone"></span>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-6">
                                     <label class="custom-subtopic-size" for="reportsToFName">Reporter's First Name</label>
                                    <input type="text" class="form-control" formControlName="reportsToFirstName"
                                        placeholder="Enter Reporting To First Name" id="reportsToFName"
                                        [ngClass]="{ 'is-invalid': submitted && f.reportsToFirstName?.errors?.pattern }">
                                    <span class="icon-position far fa-user"
                                        [ngClass]="{ 'icon-error ': submitted && f.reportsToFirstName?.errors?.pattern }"></span>
                                    <div *ngIf="submitted && f.reportsToFirstName?.errors?.pattern"
                                        class="invalid-feedback">
                                        <p>First Name is required</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                     <label class="custom-subtopic-size" for="reportsToLName">Reporter's Last Name</label>
                                    <input type="text" class="form-control" formControlName="reportsToLastName"
                                        placeholder="Enter Reporting To Last Name" id="reportsToLName"
                                        [ngClass]="{ 'is-invalid': submitted && f.reportsToLastName?.errors?.pattern }">
                                    <span class="icon-position far fa-user"
                                        [ngClass]="{ 'icon-error ': submitted && f.reportsToLastName?.errors?.pattern }"></span>
                                    <div *ngIf="submitted && f.reportsToLastName?.errors?.pattern" class="invalid-feedback">
                                        <p>Last Name is required</p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-6">
                                     <label class="custom-subtopic-size" for="reportsMail">Reporter's Email</label>
                                    <input type="email" class="form-control" formControlName="reportsToEmail"
                                        placeholder="Enter Reporting To Email" id="reportsMail"
                                        [ngClass]="{ 'is-invalid': submitted && f.reportsToEmail?.errors?.pattern }">
                                    <span class="icon-position far fa-envelope"
                                        [ngClass]="{ 'icon-error ': submitted && f.reportsToEmail?.errors?.pattern }"></span>
                                    <div *ngIf="submitted && f.reportsToEmail?.errors?.pattern" class="invalid-feedback">
                                        <p>Email is required</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                     <label class="custom-subtopic-size" for="role">Role</label>
                                    <select class="form-control" id="role" formControlName="role" *ngIf="!isSubAdmin">
                                        <option value="" disabled>Choose a Role...</option>
                                        <option *ngFor="let type of role" [value]="type.value"> {{type.name}}</option>
                                    </select>
                                    <input *ngIf="isSubAdmin" class="form-control" formControlName="role"
                                        [readonly]='isSubAdmin'>
                                </div>
                            </div>
                        </div>
                    </mat-accordion>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
        <!-- <div class="col-md-12">
            <mat-accordion displayMode="" multi class="mat-table" is-open="true">
                <mat-expansion-panel class="mt-4 " expanded="true">
                    <mat-expansion-panel-header class="mat-row p-3">
                        <div class="d-flex">
                            Surface Informations
                        </div>
                    </mat-expansion-panel-header>
                    <mat-accordion>
                        <div class="mt-3">
                            <div class="container ">
                                <div class="row form-group">
                                    <div class="col-md-4">
                                        <label for="Country">Country</label>
                                        <input type="number" class="form-control" id="Country" placeholder="Country"
                                            formControlName="country" />
                                    </div>
                                    <div class="col-md-4">
                                        <label for="city">City</label>
                                        <input type="number" class="form-control" id="city" placeholder="City"
                                            formControlName="city" />
                                    </div>
                                    <div class="col-md-4">
                                        <label for="investment">investment</label>
                                        <input type="number" class="form-control" id="investment" placeholder="investment"
                                            formControlName="investment" />
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-md-4">
                                        <label for="latitude">latitude </label>
                                        <input type="number" class="form-control" id="latitude" placeholder="latitude "
                                            formControlName="latitude" />
                                    </div>
                                    <div class="col-md-4">
                                        <label for="logitude">logitude </label>
                                        <input type="number" class="form-control" id="logitude" placeholder="logitude "
                                            formControlName="logitude" />
                                    </div>
                                    <div class="col-md-4">
                                        <label for="sonar site id">sonar site id</label>
                                        <input type="number" class="form-control" id="sonarsiteid"
                                            placeholder="sonarsiteid " formControlName="sonarsiteid" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </mat-accordion>
                </mat-expansion-panel>
            </mat-accordion>
        </div> -->
        <div class="col-md-12">
            <mat-accordion displayMode="" multi class="mat-table" is-open="true">
                <mat-expansion-panel class="mt-4" expanded="true">
                    <mat-expansion-panel-header class="mat-row p-3 background">
                        <div class="d-flex">
                            Address Informations
                        </div>
                    </mat-expansion-panel-header>
                    <mat-accordion>
                        <div>
                            <div class="row py-2">
                                <div class="col-md-4 mt-2">
                                    <label class="custom-subtopic-size" for="building">Building</label>
                                    <input type="text" class="form-control" id="building" placeholder="Building"
                                        formControlName="building"
                                        [ngClass]="{ 'is-invalid': submitted && f.building?.errors?.pattern }" />
                                    <span class="icon-position fas fa-city"
                                        [ngClass]="{ 'icon-error ': submitted && f.building?.errors }"></span>
                                    <div *ngIf="submitted && f.building.errors?.pattern" class="invalid-feedback">
                                        <p>Building is required</p>
                                    </div>
                                </div>
                                <div class="col-md-8 mt-2">
                                    <label class="custom-subtopic-size" for="street">Street</label>
                                    <input type="text" class="form-control" id="street" placeholder="Street"
                                        formControlName="street"
                                        [ngClass]="{ 'is-invalid': submitted && f.street?.errors?.pattern }" />
                                    <span class="icon-position fa fa-road"
                                        [ngClass]="{ 'icon-error ': submitted && f.street?.errors?.pattern }"></span>
                                    <div *ngIf="submitted && f.street?.errors?.pattern" class="invalid-feedback">
                                        <p>Street is required</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mt-4">
                                    <label class="custom-subtopic-size" for="state">State</label>
                                    <input type="text" class="form-control" id="state" placeholder="State"
                                        formControlName="state"
                                        [ngClass]="{ 'is-invalid': submitted && f.state?.errors?.pattern }" />
                                    <span class="icon-position  fa fa-university"
                                        [ngClass]="{ 'icon-error ': submitted && f.state?.errors?.pattern }"></span>
                                    <div *ngIf="submitted && f.state?.errors?.pattern" class="invalid-feedback">
                                        <p>State is required</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mt-4">
                                    <label class="custom-subtopic-size" for="country">Country</label>
                                    <input type="text" class="form-control" id="country" placeholder="Country"
                                        formControlName="country"
                                        [ngClass]="{ 'is-invalid': submitted && f.country?.errors?.pattern }"
                                        [readonly]='isSubAdmin' />
                                    <span class="icon-position fa fa-flag-checkered"
                                        [ngClass]="{ 'icon-error ': submitted && f.country?.errors?.pattern }"></span>
                                    <div *ngIf="submitted && f.country?.errors?.pattern" class="invalid-feedback">
                                        <p>Country is required</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mt-4">
                                    <label class="custom-subtopic-size" for="zip">Zip</label>
                                    <input type="text" class="form-control" id="zip" placeholder="Zip" formControlName="zip"
                                        [ngClass]="{ 'is-invalid': submitted && f.zip?.errors?.pattern }" />
                                    <span class="icon-position fas fa-map-pin"
                                        [ngClass]="{ 'icon-error ': submitted && f.zip?.errors?.pattern }"></span>
                                    <div *ngIf="submitted && f.zip?.errors?.pattern" class="invalid-feedback">
                                        <p>Zip is required</p>
                                    </div>
    
                                </div>
                            </div>
                        </div>
                    </mat-accordion>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
        <div class="col-md-12">
            <mat-accordion displayMode="" multi class="mat-table" is-open="true">
                <mat-expansion-panel class="mt-4 " expanded="true">
                    <mat-expansion-panel-header class="mat-row p-3 background">
                        <div class="d-flex">
                            Related Informations
                        </div>
                    </mat-expansion-panel-header>
                    <mat-accordion>
                        <div class="col-md-12 card mt-4">
                            <ngx-datatable class="bootstrap cursor-pointer" [rows]="siteRelatedInfo"
                                [columnMode]="ColumnMode.force" [headerHeight]="headerHeight" [footerHeight]="footerHeight"
                                [rowHeight]="rowHeight" [limit]="limit" [count]="sitesCount" [externalPaging]="false"
                                style=" height: 100%;width: 100%;">
    
                                <ngx-datatable-column [width]="30" [sortable]="false" [canAutoResize]="false"
                                    [draggable]="false" [resizeable]="false" [headerCheckboxable]="true"
                                    [checkboxable]="true">
                                </ngx-datatable-column>
                                <ngx-datatable-column name="Site Technical Id" prop="id" [minWidth] = "150">
                                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                                            '-'}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column name="Site Name" prop="name" [minWidth] = "200">
                                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                                            '-'}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column name="Site Id" prop="siteId" [minWidth] = "200">
                                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                                            '-'}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column name="Country" prop="country" [minWidth] = "150">
                                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                                            '-'}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column name="Sea Type" prop="seaType" [minWidth] = "180">
                                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                                            '-'}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column name="SocialValue" prop="socialValue" [minWidth] = "100">
                                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ? true :
                                            false}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                                <ngx-datatable-column name="Carbon" prop="carbon" [minWidth] = "100">
                                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ? true :
                                            false}}</span>
                                    </ng-template>
                                </ngx-datatable-column>
                            </ngx-datatable>
                        </div>
                    </mat-accordion>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
        <!-- <div class="container mt-4">
            <div class="form-group row">
                <div class="col-md-6">
                    <label for="firstName"><sup>*</sup>First Name</label>
                    <input type="text" class="form-control" formControlName="firstName" placeholder="Enter First Name"
                        [ngClass]="{ 'is-invalid': submitted && f.firstName.errors }" id="firstName">
                    <span class="icon-position far fa-user"
                        [ngClass]="{ 'icon-error ': submitted && f.firstName.errors }"></span>
                    <div *ngIf="submitted && f.firstName.errors" class="invalid-feedback">
                        <p *ngIf="f.firstName.errors.required">First Name is required</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <label for="lastName"><sup>*</sup>Last Name</label>
                    <input type="text" class="form-control" formControlName="lastName" placeholder="Enter Last Name"
                        [ngClass]="{ 'is-invalid': submitted && f.lastName.errors }" id="lastName">
                    <span class="icon-position far fa-user"
                        [ngClass]="{ 'icon-error ': submitted && f.lastName.errors }"></span>
                    <div *ngIf="submitted && f.lastName.errors" class="invalid-feedback">
                        <p *ngIf="f.lastName.errors.required">Last Name is required</p>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6">
                    <label for="email"> <sup>*</sup>Manager Email</label>
                    <input type="email" class="form-control" formControlName="email" placeholder="Enter Manager Email"
                        [ngClass]="{ 'is-invalid': submitted && f.email.errors }" id="email"
                        (ngModelChange)="emailChanged()">
                    <span class="icon-position far fa-envelope"
                        [ngClass]="{ 'icon-error ': submitted && f.email.errors }"></span>
                    <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                        <p *ngIf="f.email.errors.required"><sup>*</sup>Manager Email is required</p>
                        <p *ngIf="f.email.errors.pattern">Email must be a valid mail address</p>
                    </div>
                </div>
                <div class="col-md-6" id="id1">
                    <label for="language">Language</label>
                    <input id="language" placeholder="Search for language" type="text" class="form-control"
                        formControlName="language" [ngbTypeahead]="searchLanguage"
                        [resultFormatter]="resultFormatBandListLanguage" [inputFormatter]="inputFormatBandListLanguage"
                        (focus)="focusLang$.next($event.target.value)" (click)="clickLang$.next($event.target.value)"
                        [ngClass]="{ 'is-invalid': submitted && f.language.errors }" #instanceLang="ngbTypeahead" />
                    <div *ngIf="submitted && f.language.errors" class="invalid-feedback">
                        <div *ngIf="f.language.errors.required">Language is required</div>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6" id="id1">
                    <label for="site">Site</label>
                    <ngx-select-dropdown formControlName="site" [multiple]="true" [config]="siteConfig" [options]="sites">
                    </ngx-select-dropdown>
                </div>
                <div class="col-md-6">
                    <label for="phone">Manager Phone</label>
                    <input type="number" class="form-control" formControlName="phone" placeholder="Enter Manager Phone"
                        id="phone">
                    <span class="icon-position fas fa-phone"></span>
                </div>
            </div>
    
            <div class="form-group row">
                <div class="col-md-6">
                    <label for="reportsToFName">Reporter's First Name</label>
                    <input type="text" class="form-control" formControlName="reportsToFirstName"
                        placeholder="Enter Reporting To First Name" id="reportsToFName"
                        [ngClass]="{ 'is-invalid': submitted && f.reportsToFirstName?.errors?.pattern }">
                    <span class="icon-position far fa-user"
                        [ngClass]="{ 'icon-error ': submitted && f.reportsToFirstName?.errors?.pattern }"></span>
                    <div *ngIf="submitted && f.reportsToFirstName?.errors?.pattern" class="invalid-feedback">
                        <p>First Name is required</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <label for="reportsToLName">Reporter's Last Name</label>
                    <input type="text" class="form-control" formControlName="reportsToLastName"
                        placeholder="Enter Reporting To Last Name" id="reportsToLName"
                        [ngClass]="{ 'is-invalid': submitted && f.reportsToLastName?.errors?.pattern }">
                    <span class="icon-position far fa-user"
                        [ngClass]="{ 'icon-error ': submitted && f.reportsToLastName?.errors?.pattern }"></span>
                    <div *ngIf="submitted && f.reportsToLastName?.errors?.pattern" class="invalid-feedback">
                        <p>Last Name is required</p>
                    </div>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-6">
                    <label for="reportsMail">Reporter's Email</label>
                    <input type="email" class="form-control" formControlName="reportsToEmail"
                        placeholder="Enter Reporting To Email" id="reportsMail"
                        [ngClass]="{ 'is-invalid': submitted && f.reportsToEmail?.errors?.pattern }">
                    <span class="icon-position far fa-envelope"
                        [ngClass]="{ 'icon-error ': submitted && f.reportsToEmail?.errors?.pattern }"></span>
                    <div *ngIf="submitted && f.reportsToEmail?.errors?.pattern" class="invalid-feedback">
                        <p>Email is required</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <label for="role">Role</label>
                    <select class="form-control" id="role" formControlName="role" *ngIf="!isSubAdmin">
                        <option value="" disabled>Choose a Role...</option>
                        <option *ngFor="let type of role" [value]="type.value"> {{type.name}}</option>
                    </select>
                    <input *ngIf="isSubAdmin" class="form-control" formControlName="role" [readonly]='isSubAdmin'>
                </div>
            </div>
    
            <div class="row  form-group mt-3">
                <div class="col-md-12">
                    <label for="address">Address </label>
                </div>
            </div>
            <div class="row py-2">
                <div class="col-md-4 mt-2">
                    <label for="building">Building</label>
                    <input type="text" class="form-control" id="building" placeholder="Building" formControlName="building"
                        [ngClass]="{ 'is-invalid': submitted && f.building?.errors?.pattern }" />
                    <span class="icon-position fas fa-city"
                        [ngClass]="{ 'icon-error ': submitted && f.building?.errors }"></span>
                    <div *ngIf="submitted && f.building.errors?.pattern" class="invalid-feedback">
                        <p>Building is required</p>
                    </div>
                </div>
                <div class="col-md-8 mt-2">
                    <label for="street">Street</label>
                    <input type="text" class="form-control" id="street" placeholder="Street" formControlName="street"
                        [ngClass]="{ 'is-invalid': submitted && f.street?.errors?.pattern }" />
                    <span class="icon-position fa fa-road"
                        [ngClass]="{ 'icon-error ': submitted && f.street?.errors?.pattern }"></span>
                    <div *ngIf="submitted && f.street?.errors?.pattern" class="invalid-feedback">
                        <p>Street is required</p>
                    </div>
                </div>
                <div class="col-md-4 mt-4">
                    <label for="state">State</label>
                    <input type="text" class="form-control" id="state" placeholder="State" formControlName="state"
                        [ngClass]="{ 'is-invalid': submitted && f.state?.errors?.pattern }" />
                    <span class="icon-position  fa fa-university"
                        [ngClass]="{ 'icon-error ': submitted && f.state?.errors?.pattern }"></span>
                    <div *ngIf="submitted && f.state?.errors?.pattern" class="invalid-feedback">
                        <p>State is required</p>
                    </div>
                </div>
                <div class="col-md-4 mt-4">
                    <label for="country">Country</label>
                    <input type="text" class="form-control" id="country" placeholder="Country" formControlName="country"
                        [ngClass]="{ 'is-invalid': submitted && f.country?.errors?.pattern }"  [readonly]='isSubAdmin' />
                    <span class="icon-position fa fa-flag-checkered"
                        [ngClass]="{ 'icon-error ': submitted && f.country?.errors?.pattern }"></span>
                    <div *ngIf="submitted && f.country?.errors?.pattern" class="invalid-feedback">
                        <p>Country is required</p>
                    </div>
                </div>
                <div class="col-md-4 mt-4">
                    <label for="zip">Zip</label>
                    <input type="text" class="form-control" id="zip" placeholder="Zip" formControlName="zip"
                        [ngClass]="{ 'is-invalid': submitted && f.zip?.errors?.pattern }" />
                    <span class="icon-position fas fa-map-pin"
                        [ngClass]="{ 'icon-error ': submitted && f.zip?.errors?.pattern }"></span>
                    <div *ngIf="submitted && f.zip?.errors?.pattern" class="invalid-feedback">
                        <p>Zip is required</p>
                    </div>
    
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right mb-2 pt-4">
                    <button type="reset" (click)="listRecord()" class="btn btn-secondary mr-2">Cancel</button>
                    <button type="submit" class="btn btn-primary text-right">Update</button>
                </div>
            </div>
        </div> -->
    </form>
</div>
<div id="nb-global-spinner" *ngIf="!sitesDataLoaded" class="align-items-center d-flex h-100 justify-content-center spinner w-100" style="z-index: 9999;">
    <div class="d-flex flex-column" style="width: 130px;height: 187px;" style="z-index: 9999;">
      <img src="assets/images/sea-loading.gif" alt="" srcset="">
      <img src="assets/images/sodexo.png" alt="">
    </div>
  </div>
