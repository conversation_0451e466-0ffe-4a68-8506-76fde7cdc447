import { AfterViewInit, Component, OnDestroy, OnInit, Output, TemplateRef, ViewChild, ElementRef } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { NgxSpinnerService } from 'ngx-spinner';
import { DataService } from 'src/app/shared/services/data.service';
import * as XLSX from 'xlsx';
import { Subscription } from 'rxjs';
import { Title } from '@angular/platform-browser';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { UnitConversionService } from 'src/app/shared/services/unit-conversion.service';
import { DatePipe } from '@angular/common';
import {
  AUTO_STYLE,
  animate,
  state,
  style,
  transition,
  trigger
} from '@angular/animations';
import { distanceUnits } from 'src/app/shared/constants/constant';
import { MatDialog } from '@angular/material/dialog';
import * as _ from 'lodash';
import { TranslateService } from '@ngx-translate/core';
import { CommaPipe } from 'src/app/shared/pipes/search.pipe';
import { percentageValidator, resultNotLessThanZero, valueEqualOrGreater } from 'src/app/carbon/services/customvalidators/percentage.validator';
import { EmissionfactorserviceService } from 'src/app/carbon/services/emissionfactorservice.service';
import { GeneralInfoService } from 'src/app/carbon/services/general-info.service';
import { LanguageTranslateService } from 'src/app/carbon/services/language-translate.service';
import { QuestionresultsService } from 'src/app/carbon/services/questionresults.service';
import { QuestionsService } from 'src/app/carbon/services/questions.service';
import { CleaningGeneralInformationService } from '../../services/cleaning-general-information.service';
import { CarbonCleaningQuestionsService } from '../../services/carbon-cleaning-questions.service';
import { CleaningquestionresultService } from '../../services/cleaningquestionresult.service';
import { SeasiteService } from 'src/app/sea/services/seasite.service';
const DEFAULT_DURATION = 300;
const topicId = 2;
const ELEMENT_DATA: any[] = [];

@Component({
  selector: 'app-products-water-consumption',
  templateUrl: './products-water-consumption.component.html',
  styleUrls: ['./products-water-consumption.component.scss'],
  animations: [
    trigger('collapse', [
      transition('false => true', animate(DEFAULT_DURATION + 'ms ease-in')),
      transition('true => false', animate(DEFAULT_DURATION + 'ms ease-out')),

      state('false', style({ height: '100px', visibility: 'visible' })),
      state('true', style({ height: '0', display: 'none' })),
    ])
  ]
})
export class ProductsWaterConsumptionComponent {
  @ViewChild('errorMessage') errorMessage: TemplateRef<any>;
  @ViewChild('table1') table1: MatTable<any>;
  @ViewChild('approved') approved!: ElementRef;
  @ViewChild('areYouSure') areyousure: ElementRef;
  errorMessageList = [];
  disabledValue = true;
  lastUpdated = new Date();
  firstForm !: UntypedFormGroup;
  nearForm !: UntypedFormGroup;
  farForm!: UntypedFormGroup;
  isEditableNew = true;
  isLoading = true;
  istype = true;
  PeriodicElement: any;
  questionResult: any = [];
  questionsList: any = [];
  formQuestions: any;
  tableQuestion: any;
  id: any;
  dataSource0: any;
  displayedColumns0: string[] = ['name', 'local', 'near', 'far', 'localCo2e', 'nearCo2e', 'farCo2e'];
  tableDetails: any[];
  istable1 = true;
  istable2 = false;
  language = this.service.languageIdentifier;
  subscription: Subscription = new Subscription();
  dialogRef: any;
  localTotal: any;
  nearTotal: any;
  farTotal: any;
  event: void;
  showInputColor = false;
  volUnit = 1;
  monUnit = '1';
  distanceUnit = 1;
  dialogRefApproved: any;
  isAprrover = false;
  dateRange: any;
  selected:any = 1;
  // table 2
  @ViewChild('errorMessage1') errorMessage1: TemplateRef<any>;
  errorMessageList1 = [];
  // , 'unit'
  // displayedColumns: string[] = ['sname', 'unit', 'value', 'sfld', 'sfnd', 'sffd', 'pgas', 'udat', 'local', 'near', 'far'];
  // displayedColumnsTopHeader = ['th1', 'th2', 'th3', 'th4', 'th5', 'th6', 'th7', 'th8', 'th9'];
  displayedColumns: string[] = ['sname',  'value', 'pgas'];
  displayedColumnsTopHeader = [];
  displayedColumns2: string[] = ['sname', 'unit', 'value', 'sfld', 'sfnd', 'sffd','local', 'near', 'far','totalall'];
  displayedColumnsTopHeader2 = ['th1', 'th2', 'th3', 'th4'];
  currency = [];
  dataSource: any = [];
 
fourthForm !: UntypedFormGroup;
  supplyChainForm !: UntypedFormGroup;
  showSecondTable = true;

  tableDetails1 = [];
  id1: any;
  expandMC = true;
  units = [
    {
      id: 1,
      value: 'kg'
    },
    {
      id: 2,
      value: 'metric tons'
    },
    {
      id: 3,
      value: 'lb'
    },
  ];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  localf: UntypedFormGroup;
  distanceUnits = distanceUnits.sort((a: any, b: any) => a.order > b.order ? 0 : -1);
  tableDetail: any;
  tableDetail1: any;
  tableDetail2: any;
  tableDetails2: any;
  id3: any;
  supplyChainForm1 !: UntypedFormGroup;
  dataSource1: any;
  showThirdTable = true;
  dialogRefareyousure: any;
  selectedDate = false;
  sidenavOpened: boolean = false;
hygienicProductsForm!: UntypedFormGroup;
hygienicDataSource: any = [];
hygienicDisplayedColumns: string[] = ['productName', 'vppfrp','pgas'];
hygienicdisplayedColumnsTopHeader = ['th4']
hygienicProducts = [];
  hygienicTableDetail: any;
  hygienicQuestionId: any;
  nonElectricEquipmentForm!: UntypedFormGroup;
  nonElectricDataSource: any = [];
  nonElectricDisplayedColumns: string[] = ['equipmentName', 'purchasedVolume', 'usedVolume', 'disposedVolume','pgas'];
  nonElectricTableDetail: any;
  nonElectricTableDetails: any[] = [];
  nonElectricQuestionId: any;
  nonElectricdisplayedColumnsTopHeader = ['th4'];
  expandNonElectric = false;
  chemicalProductsForm: UntypedFormGroup;
  chemicalDataSource: MatTableDataSource<any>;
  chemicalDisplayedColumns: string[] = ['pucs', 'ppfrp','pgas'];
  chemicalTableDetail: any;
  chemicalQuestionId: any;
  chemicaldisplayedColumnsTopHeader = ['th4'];
  ppeForm: UntypedFormGroup;
ppeDataSource: MatTableDataSource<any>;
ppeDisplayedColumns: string[] = ['ppeName', 'vpfpr', 'vdrp','pgas'];
ppeTableDetail: any;
ppeQuestionId: any;
ppeTableDetails: any[] = [];
ppedisplayedColumnsTopHeader = ['th4'];
volumeOfHydienicProducts: number = 0;
localCo2eSum: number = 0;
localCo2eSumWithoutRoad: number = 0;
nearCo2eSum: number = 0;
nearCo2eSumWithoutRoad: number = 0;
farCo2eSum: number = 0;
farCo2eSumWithoutRoad: number = 0;
volumeOfNonElectricEquipment: number = 0;
volumeOfPPE: number = 0;

  constructor(
    private fb: UntypedFormBuilder,
    private emissionService: EmissionfactorserviceService,
    public service: CleaningGeneralInformationService,
    private questionService: CarbonCleaningQuestionsService,
    private questionResultService: CleaningquestionresultService,
    private toaster: ToastrService,
    private spinner: NgxSpinnerService,
    private dataService: DataService,
    private ts: Title,
    private modalService: NgbModal,
    private route: Router,
    private uc: UnitConversionService,
    private dialog: MatDialog,
    // tslint:disable-next-line:variable-name
    public _datePipe: DatePipe,
    private translate: TranslateService,
    private lts: LanguageTranslateService,
    private comma: CommaPipe,
    private site: SeasiteService,
  ) {
    translate.use('en');
    translate.setTranslation('en', this.lts.state);
    this.ts.setTitle('Carbon - Products Consumption');
    this.currency = this.service.currencyList;
    
    if (this.service.showCalculation) {
    } else {
      // 'unit',
      this.displayedColumns0 = ['name', 'local', 'near', 'far'];
      // this.displayedColumns = ['sname', 'unit', 'value', 'sfld', 'sfnd', 'sffd'];
      // this.displayedColumnsTopHeader = ['th1', 'th2', 'th3', 'th4'];
      this.displayedColumns = ['sname', 'unit', 'value'];
      this.displayedColumnsTopHeader = ['th1', 'th2'];
      this.displayedColumns2 = ['sname', 'unit', 'value', 'sfld', 'sfnd', 'sffd'];
      this.displayedColumnsTopHeader2 = ['th1', 'th2', 'th3', 'th4'];
    }
  }

  ngAfterViewInit() {
  }
  // tslint:disable-next-line:use-lifecycle-interface
  ngOnInit(): void {
    this.spinner.show();
    this.getQuestionResult();
    // this.supplyChainForm = this.fb.group({
    //   Rows: this.fb.array(ELEMENT_DATA.map(val => this.mainTableForm(val)
    //   )) // end of fb array
    // }); // end of form group cretation
    this.firstForm = this.fb.group({
      Rows: this.fb.array([])
    });
    this.localf = this.fb.group({
      local1: [150]
    });
    this.nearForm = this.fb.group({
      // near1: [150],
      near2: [500]
    });
    this.farForm = this.fb.group({
      // far1: [500],
      far2: [20000]
    });
    
  // Initialize the non-electric equipment form
  this.nonElectricEquipmentForm = this.fb.group({
    Rows: this.fb.array([])
  });
  
  // Initialize the hygienic products form
  this.hygienicProductsForm = this.fb.group({
    Rows: this.fb.array([])
  });
  
  // Initialize the chemical products form
  this.chemicalProductsForm = this.fb.group({
    Rows: this.fb.array([])
  });
  
  // Initialize the PPE form
  this.ppeForm = this.fb.group({
    Rows: this.fb.array([])
  });
  
    this.initializeHygienicProductsForm();
    this.initiateChemicalProductsTable();
  }

  getQuestionResult() {
    const payload = {
      topic: 2,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      date: this.service.endDate
    };
    // const results$ = this.questionResultService.getQuestionsResultsById(payload).subscribe((res: any) => {
    //   this.isLoading = false;
    //   this.spinner.hide();
    //   this.questionResult = res.data;
    //   this.id = res.data.length > 0 ? res.data[0].id : undefined;
    //   this.getQuestions(topicId, res);
    // },
    //   err => {
    //     this.spinner.hide();
    //     this.getQuestions(topicId);
    //   });
    const results$ = this.questionResultService.getCleaningQuestionsResultsById(payload).subscribe((res: any) => {
      this.isLoading = false;
      this.spinner.hide();
      this.questionResult = res?.data || [];
      this.id = res?.data && res?.data?.length > 0 ? res.data[0].id : undefined;
      this.getQuestions(topicId, res);
      this.getQuestionDates();
    },
      err => {
        this.spinner.hide();
        this.getQuestions(topicId);
        this.getQuestionDates();
      });
    this.subscription.add(results$);
  }
  getQuestions(type, res?: any) {
    this.spinner.show();
    const questions$ = this.questionService.getCleaningQuestionByTopicId(type).subscribe(data => {
      
      this.spinner.hide();
      this.questionsList = data;
      this.tableQuestion = (this.questionsList || []).filter(q => q.question_format === 1 && q.is_active).
        sort((a, b) => a.sequence - b.sequence);
      if (this.tableQuestion.length > 0) {
        this.initiateTable();
        this.initiateMainTable();
        this.initiatePercentageTable();
        
        // Call the hygienic products function
        this.initiateHygienicProductsTable();
        
        // Call the new chemical products function
        this.initiateChemicalProductsTable();
        
        // Call the new non-electric equipment function
        this.initiateNonElectricEquipmentTable();
        
        // Call the PPE table function
        this.initiatePpeTable();
      }
    }, (err: any) => {
      this.spinner.hide();
      throw err;
    });
    this.subscription.add(questions$);
  }
  initiateTable() {
    this.spinner.show();
    this.isLoading = true;
    this.tableDetail = this.tableQuestion[0];
    this.tableDetails = this.tableQuestion[0].options || [];
    this.firstForm = this.fb.group({
      Rows: this.fb.array((this.tableDetails || []).map((val, i) => this.checkQuestionResult(val, i)
      )) // end of fb array
    }); // end of form group cretation
    // After patching initial value and then calculation
    this.changesInLFN();
    this.isLoading = false;
    this.spinner.hide();
    this.dataSource0 = new MatTableDataSource((this.firstForm.get('Rows') as UntypedFormArray).controls);
  }
  changesInLFN() {
    const rows = this.firstForm.value.Rows;
    rows.map((row: any, i: number) => {
      if (i === 0) {
        return;
      }
      if (row.hasOwnProperty('local')) {
        this.emissionCalculation(row.local, 'localCo2e', i);
      }
      if (row.hasOwnProperty('near')) {
        this.emissionCalculation(row.near, 'nearCo2e', i);
      }
      if (row.hasOwnProperty('far')) {
        this.emissionCalculation(row.far, 'farCo2e', i);
      }
    });
    if (this.supplyChainForm1) {
      const formValue = this.supplyChainForm1.value.Rows;
      (formValue || []).forEach((f, i) => {
        this.onValueChange1(i);
      });
    }
  }
  checkQuestionResult(question: any, i) {
    const options = (this.tableDetail.language[this.language] ? this.tableDetail.language[this.language].options : this.tableDetail.options)
      .find((option: any) => option.question_id === question.question_id);
    question.name = options.name;
    question.description = options.description;
    if (this.questionResult.length > 0) {
      // tslint:disable-next-line:max-line-length
      const result = (this.questionResult || []).filter(q => q.question === this.tableQuestion[0].id && q.sequence === this.tableQuestion[0].sequence && q.question_format === this.tableQuestion[0].question_format);

      let obj = [];
      try {
        obj = JSON.parse(result[0]?.answer);

      } catch (e) {
        obj = [];
      }
      const answer = (obj || []).filter(r => r.question_id === question.question_id);
      if (i === 0) {
        this.localf.patchValue({
          local1: answer[0]?.localCo2e || 150
        });
        this.nearForm.patchValue({
          near2: obj[0]?.near?.near2 || 500
        });
        this.farForm.patchValue({
          far2: obj[0]?.far?.far2 || 20000
        });
      }

      if (result.length > 0) {
        let unit;
        try {
          unit = JSON.parse(result[0]?.unit);
        } catch (e) {
          unit = result[0]?.unit;
        }
        this.distanceUnit = unit?.distance || this.distanceUnit;
        return this.fb.group({
          name: new UntypedFormControl(question.name),
          toolTip: new UntypedFormControl(question.description),
          question_id: new UntypedFormControl(question.question_id),
          local: new UntypedFormControl(answer[0]?.local),
          near: new UntypedFormControl(answer[0]?.near),
          far: new UntypedFormControl(answer[0]?.far),
          localCo2e: new UntypedFormControl(answer[0]?.localCo2e),
          nearCo2e: new UntypedFormControl(i === 0 ? obj[0]?.near?.near2 : answer[0]?.nearCo2e),
          farCo2e: new UntypedFormControl(i === 0 ? obj[0]?.far?.far2 : answer[0]?.farCo2e)
        });
      } else {
        return this.fb.group({
          name: new UntypedFormControl(question.name),
          toolTip: new UntypedFormControl(question.description),
          question_id: new UntypedFormControl(question.question_id),
          local: new UntypedFormControl(i === 0 ? 150 : null),
          near: new UntypedFormControl(i === 0 ? this.nearForm.value : null),
          far: new UntypedFormControl(i === 0 ? this.farForm.value : null),
          localCo2e: new UntypedFormControl(i === 0 ? 150 : null),
          nearCo2e: new UntypedFormControl(i === 0 ? 500 : null),
          farCo2e: new UntypedFormControl(i === 0 ? 20000 : null)
        });
      }
    } else {
      return this.fb.group({
        name: new UntypedFormControl(question.name),
        toolTip: new UntypedFormControl(question.description),
        question_id: new UntypedFormControl(question.question_id),
        local: new UntypedFormControl(i === 0 ? 150 : i === 1 ? 100 : 0),
        near: new UntypedFormControl(i === 0 ? this.nearForm.value : i === 1 ? 100 : 0),
        far: new UntypedFormControl(i === 0 ? this.farForm.value : i === 1 ? 10 : i === 3 ? 90 : 0),
        localCo2e: new UntypedFormControl(i === 0 ? 150 : null),
        nearCo2e: new UntypedFormControl(i === 0 ? 500 : null),
        farCo2e: new UntypedFormControl(i === 0 ? 20000 : null)
      });
    }
  }
  changeLocalFarNear(type) {
    const form = (this.firstForm.get('Rows') as UntypedFormArray).at(0);
    if (type === 'local') {
      form.patchValue({
        localCo2e: this.localf.value.local1
      });
    }
    if (type === 'near') {
      form.patchValue({
        nearCo2e: this.nearForm.value.near2
      });
    }
    if (type === 'far') {
      form.patchValue({
        farCo2e: this.farForm.value.far2
      });
    }
    this.changesInLFN();
  }

  handleFileSelect(event, condition) {
    const files = event.target.files;
    if (!files.length) {
      return 0;
    }
    const file = files[0];
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = (e: any) => {
      const data = new Uint8Array(e.target.result);
      const arr = new Array();
      for (let i = 0; i !== data.length; ++i) { arr[i] = String.fromCharCode(data[i]); }
      const bstr = arr.join('');
      const workbook = XLSX.read(bstr, { type: 'binary' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const objData = XLSX.utils.sheet_to_json(worksheet, { raw: true });
      const filesKeys = Object.keys(objData[0]);
      if (!filesKeys.includes('system_id')) {
        this.toaster.error('Files doesn\'t contain question id');
        return;
      }
      if (condition) {
        this.getSupplyObjData(objData);
      } else {
        this.getSupplyChainDistanceBased(objData);
      }
      event.target.value = '';
    };
  }

  checkValidatity(questionForm): boolean {
    this.errorMessageList = [];
    const values = this[questionForm].value.Rows;
    let near = 0;
    let local = 0;
    let far = 0;
    values.map((obj: any, id: number) => {
      if (obj.near === null || obj.local === null || obj.far === null) {
        this.errorMessageList.push({ name: obj.name });
      }
      if (id > 0) {
        near += obj.near;
        local += obj.local;
        far += obj.far;
      }
    });
    if (near > 100) {
      // tslint:disable-next-line:quotemark
      // this.toaster.error("sum of near not execeed the 100%");
      this.errorMessageList.push({ name: `Sum of Near is execeeding the 100% - The sum is ${near}%` });
      // return;
    }
    if (local > 100) {
      // tslint:disable-next-line:quotemark
      // this.toaster.error("sum of local not execeed the 100%");
      this.errorMessageList.push({ name: `Sum of Local is execeeding the 100% - The sum is ${local}%` });
      // return;
    }
    if (far > 100) {
      // tslint:disable-next-line:quotemark
      // this.toaster.error("sum of far not execeed the 100%");
      this.errorMessageList.push({ name: `Sum of Far is execeeding the 100% - The sum is ${far}%` });
      // return;
    }
    return this.errorMessageList.length ? true : false;
  }
  emissionCalculation(value, to, i) {
    // tslint:disable-next-line:no-shadowed-variable
    const calcForm = this.firstForm.value.Rows.filter((i, index) => index !== 0);
    this.localTotal = calcForm.reduce((accumulator, obj) => {
      return accumulator + obj.local;
    }, 0);
    this.nearTotal = calcForm.reduce((accumulator, obj) => {
      return accumulator + obj.near;
    }, 0);
    this.farTotal = calcForm.reduce((accumulator, obj) => {
      return accumulator + obj.far;
    }, 0);

    const currentRow = this.tableDetails[i];
    const emissionType = currentRow.emissionType || {};
    const emissionValue = (this.emissionService.carbonEmissionFactor || []).find(e => e.emission_type === emissionType.id);
    const myForm = (this.firstForm.get('Rows') as UntypedFormArray).at(i);
    let columnFirstValue = 0;
    columnFirstValue = (this.firstForm.get('Rows') as UntypedFormArray).at(0).get(to).value || 0;
    columnFirstValue = this.uc.milesToKmConversion(this.distanceUnit, columnFirstValue);
    // const cal = ((((columnFirstValue * value) / 100) * (emissionValue?.value || 0))) / 1000;
    const cal = (columnFirstValue * value * (emissionValue?.value || 0)) / 1000;
    myForm.patchValue({
      [to]: cal.toFixed(3)
    });

    if(this.localCo2eSum != 0){
      this.localCo2eSum = 0;
    }
    if(this.nearCo2eSum != 0){
      this.nearCo2eSum = 0;
    }
    if(this.farCo2eSum != 0){
      this.farCo2eSum = 0;
    }
    if(this.localCo2eSumWithoutRoad != 0){
      this.localCo2eSumWithoutRoad = 0;
    }
    if(this.nearCo2eSumWithoutRoad != 0){
      this.nearCo2eSumWithoutRoad = 0;
    }
    if(this.farCo2eSumWithoutRoad != 0){
      this.farCo2eSumWithoutRoad = 0;
    }
    this.firstForm.value.Rows.forEach((obj, idx) => {
      const localCo2eValue = parseFloat(obj.localCo2e || 0);
      const nearCo2eValue = parseFloat(obj.nearCo2e || 0);
      const farCo2eValue = parseFloat(obj.farCo2e || 0);

      if (idx !== 0) {
        this.localCo2eSum += localCo2eValue;
        this.nearCo2eSum += nearCo2eValue;
        this.farCo2eSum += farCo2eValue;
      }
      if (idx !== 0 && idx !== 1) {
        this.localCo2eSumWithoutRoad += localCo2eValue;
        this.nearCo2eSumWithoutRoad += nearCo2eValue;
        this.farCo2eSumWithoutRoad += farCo2eValue;
      }
    });
  }


  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  close() {
    this.dialogRef.close();
  }
  initiatePercentageTable() {
    this.spinner.show();
    this.tableQuestion.forEach((details: any, index: number) => {
      if (details.sequence === 3) {
        this.tableDetail2 = this.tableQuestion[index];
        this.tableDetails2 = this.tableQuestion[index].options || [];
      }
    });
    this.questionResult.forEach((result: any) => {
      this.tableQuestion.forEach((question: any) => {
        if (question.sequence === 3 && result.question === question.id) {
          this.id3 = result.id;
        }
      });
    });
    this.supplyChainForm1 = this.fb.group({
      Rows: this.fb.array((this.tableDetails2 || []).map((val, i) => this.checkSupplyChainQuestionResult2(val, i)
      )) // end of fb array
    });
    this.isLoading = true;
    const formValue1 = this.supplyChainForm.value.Rows;
    (formValue1 || []).forEach((f, i) => {
      if(f){
        this.onValueChange(f.inputValue, i);
      }
    });
    this.isLoading = false;
    this.updateTableSupply();
    const formValue = this.supplyChainForm1.value.Rows;
    (formValue || []).forEach((f, i) => {
      // this.onValueChange(f.inputValue, i);
      this.calculateDistanceEmission(i, 'localCo2e', 'sfld', 'local');
      this.calculateDistanceEmission(i, 'nearCo2e', 'sfnd', 'near');
      this.calculateDistanceEmission(i, 'farCo2e', 'sffd', 'far');
    });
    this.updateTable2Supply();
  }

  initiateMainTable() {
    this.spinner.show();
    this.isLoading = true;
    this.tableQuestion.forEach((details: any, index: number) => {
      if (details.sequence === 2) {
        this.tableDetail1 = this.tableQuestion[index];
        this.tableDetails1 = this.tableQuestion[index].options || [];
      }
    });
    const answer = [];
    this.questionResult.forEach((result: any) => {
      this.tableQuestion.forEach((question: any) => {
        if (question.sequence === 2 && result.question === question.id) {
          this.id1 = result.id;
        }
      });
    });
    // if (answer.length > 0) {
    //   this.id1 = answer[0].id;
    // }
    this.supplyChainForm = this.fb.group({
      Rows: this.fb.array((this.tableDetails1 || []).map((val, i) => this.checkSupplyChainQuestionResult(val, i)
      )) // end of fb array
    }); // end of form group cretation
    const formValue = this.supplyChainForm.value.Rows;
    (formValue || []).forEach((f, i) => {
      if(f){
        this.onValueChange(f.inputValue, i);
      }
      // this.calculateDistanceEmission(i, 'localCo2e', 'sfld', 'local');
      // this.calculateDistanceEmission(i, 'nearCo2e', 'sfnd', 'near');
      // this.calculateDistanceEmission(i, 'farCo2e', 'sffd', 'far');
    });
    this.isLoading = false;
    this.updateTableSupply();
    // this.dataSource = new MatTableDataSource((this.supplyChainForm.get('Rows') as FormArray).controls);
    this.spinner.show();
    setTimeout(() => {
      this.expandMoreOrLess();
      this.spinner.hide();
    }, 300);
    setTimeout(() => this.dataSource.paginator = this.paginator);
    // this.dataSource.paginator = this.paginator;
    // this.spinner.hide();
  }

  getSupplyObjData(data) {
    if (data.length > 0) {
      const supplyObjData = [];
      const len = (this.supplyChainForm.get('Rows') as UntypedFormArray).length;
      for (let i = 0; i < len; i++) {
        (this.supplyChainForm.get('Rows') as UntypedFormArray).at(i).get('inputValue').setValue(0);
      }
      data.forEach((item, i) => {
        item.Value = !!item.Value && item.Value.toString().includes(',') ? item.Value.replace(',', '.') : item.Value;
        let unit = item?.Unit === 'lb' ? 3 : item?.Unit === 'metric tons' ? 2 : item?.Unit === 'kg' ? 1 : item?.Unit;
        const convertedValue = unit === 1 ? item.Value : this.uc.kgConversion(unit, item.Value);
        if (typeof unit === 'string') {
          unit = this.currency.find((cur: any) => cur.name === unit)?.id || unit;
        }
        i = this.indexReturnFromstring(item.system_id, 'tableDetails1');
        (this.supplyChainForm.get('Rows') as UntypedFormArray).at(i).patchValue({
          // name: null,
          unit,
          inputValue: this.tableDetails1[i]?.isDisabled ? 0 : item.Value === 'null' ? '' : item.Value,
          value: convertedValue,
          question_id: this.tableDetails1[i]?.question_id,
          // description: null,
          isDisabled: this.tableDetails1[i]?.isDisabled || false,
          unitType: this.tableDetails1[i]?.unitType,
          supplygroupName: this.tableDetails1[i]?.supplygroupName,
          subGroup: this.tableDetails1[i]?.subGroup,
          priority: this.tableDetails1[i]?.priority,
          isExpanded: false,
          icon: this.tableDetails1[i]?.icon,
        });
      });
      // this.supplyChainForm.reset();
      // this.supplyChainForm = this.fb.group({
      //   Rows: this.fb.array((supplyObjData || []).map((val, i) => this.mainTableForm(val, i)
      //   ))
      // });
      const formValue = this.supplyChainForm.value.Rows;
      (formValue || []).forEach((f, i) => {
        if(f){
          this.onValueChange(f.inputValue, i);
        }
      });
      this.expandMC = true;
      this.expandMoreOrLess();
      this.updateTableSupply();
    }
  }
  getSupplyChainDistanceBased(data) {
    if (data.length > 0) {
      data.forEach((item, i) => {
        let unit = item?.Unit === 'lb' ? 3 : item?.Unit === 'metric tons' ? 2 : item?.Unit === 'kg' ? 1 : item?.Unit;
        if (typeof unit === 'string') {
          unit = this.currency.find((cur: any) => cur.name === unit)?.id || unit;
        }
        i = this.indexReturnFromstring(item.system_id, 'tableDetails2');
        this.getForm1(i).patchValue({
          unit,
          sfld: item['Share from local distribution %'] === 'null' ? 0 : item['Share from local distribution %'],
          sfnd: item['Share from near distribution %'] === 'null' ? 0 : item['Share from near distribution %'],
          sffd: item['Share from far distribution %'] === 'null' ? 0 : item['Share from far distribution %'],
        });
      });
      const formValue = this.supplyChainForm1.value.Rows;
      (formValue || []).forEach((f, i) => {
        this.onValueChange1(i);
      });
    }
  }
  exportCsvData(form, table, condition) {
    const data = _.cloneDeep(this[form].value.Rows);
    const csvData = [];
    data.forEach((item, index) => {
      // index = this.indexReturnFromstring(item?.question_id, table);
      if (this[table][index].unitType.id === 1) {

        if (item.unit === 1) { item.unit = 'kg'; }
        else if (item.unit === 2) { item.unit = 'metric tons'; }
        else if (item.unit === 3) { item.unit = 'lb'; }

      } else if (this[table][index].unitType.id === 2) {
        item.unit = 'L';
      } else if (this[table][index].unitType.id === 3) {
        // tslint:disable-next-line:triple-equals
        const currency = this.currency.find((cur: any) => cur.id == item.unit);
        item.unit = currency?.name;
      } else if (this[table][index].unitType.id === 4) {
        item.unit = '%';
      }
      if (condition) {
        if (item.isDisabled) {
          return;
        }
        csvData.push({
          'Sl.No': index + 1,
          system_id: item.question_id || '',
          Name: item.name || '',
          Unit: item.unit || '',
          required: this[table][index]?.isRequired ? 'yes' : 'no',
          Value: this.comma.transform(item.inputValue || 0),
          Description: item.toolTip || '',
        });
      } else {
        csvData.push({
          'Sl.No': index + 1,
          system_id: item.question_id || '',
          Name: item.name || '',
          Unit: item.unit || '',
          required: this[table][index]?.isRequired ? 'yes' : 'no',
          'Share from local distribution %': item.sfld || 0,
          'Share from near distribution %': item.sfnd || 0,
          'Share from far distribution %': item.sffd || 0,
          Description: item.toolTip || '',
        });
      }
    });
    const workBook = XLSX.utils.book_new();
    const workSheet = XLSX.utils.json_to_sheet(csvData);
    XLSX.utils.book_append_sheet(workBook, workSheet, 'data');
    XLSX.writeFile(workBook, `${this.service.siteInfo.name}-${condition ? 'supply_chain_table_1' : 'supply_chain_table_2'}-${this._datePipe.transform(new Date(), 'medium')}.xlsx`);
  }

  getQuestionDates(){
    var siteId = this.service.siteId
    this.questionResultService.getQuestionDate(siteId).subscribe((res: any) => {     
      const data = _.uniqBy(res, 'start_date');
      this.dateRange = data;
     },
      err => {
      });
  }
  isApprovedPopup() {
    this.getQuestionDates();
    this.dialogRefApproved = this.modalService.open(this.approved, {
      centered: true,
      windowClass: 'approved-dialog'
    });
  }
  closeisAprroved() {
    this.dialogRefApproved.close();
  }

  updateQuestiopnApproved(data:any){
    if(this.selected == 1){
      return; 
    }
    var selectedDate = JSON.parse(this.selected);
    
    const payload:any = {
      siteId:this.service.siteId,
      isApprovedBy:data,
      topic: 2,
      start_date: selectedDate.start_date,
      end_date: selectedDate.end_date,
      service: "Soft FM"
    }
    if(data == true){
      payload.isApprovedBy == true
      payload.isReseted = 0;
      this.questionResultService.updateQuestiopnApproved(payload).subscribe((res: any) => {
        this.toaster.success('Site approved successfully');
        this.closeisAprroved();
        this.openMessageclose();
        window.location.reload();
        // this.getQuestionResult();
       },
        err => {
          this.toaster.error(err);
        });
    }
    
    if(data == false){    
      payload.isApprovedBy == false
      payload.isReseted = 1;
      this.questionResultService.updateQuestiopnApproved(payload).subscribe((res: any) => {
        this.toaster.success('Site rejected successfully');
        this.closeisAprroved();
        this.openMessageclose();
        window.location.reload();
        // this.getQuestionResult();
       },
        err => {
          this.toaster.error(err);
        });
    }
  }
  
  checkSupplyChainQuestionResult(question, i) {
    if (this.questionResult.length > 0) {
      // tslint:disable-next-line:max-line-length
      const result = (this.questionResult || []).filter(q => q.question === this.tableDetail1.id && q.sequence === this.tableDetail1.sequence && q.question_format === this.tableDetail1.question_format);
      let obj = [];
      let unit;
      try {
        obj = JSON.parse(result[0]?.answer);
      } catch (e) {
        obj = [];
      }
      const answer = (obj || []).filter(r => r.question_id === question.question_id);
      if (result.length > 0) {
        try {
          unit = JSON.parse(result[0]?.unit);
        } catch (e) {
          unit = result[0]?.unit;
        }
        this.volUnit = unit?.weight || this.volUnit;
        this.monUnit = unit?.currency || this.monUnit;
        const parsedAnswer = answer[0];

        if(parsedAnswer){
          parsedAnswer.subGroup = question?.subGroup;
          parsedAnswer.supplygroupName = question?.supplygroupName;
        }

        return this.mainTableForm(parsedAnswer, i);
      } else {
        return this.mainTableForm(question, i);
      }
    } else {
      return this.mainTableForm(question, i);
    }
  }
  checkSupplyChainQuestionResult2(question, i) {
    if (this.questionResult.length > 0) {
      // tslint:disable-next-line:max-line-length
      const result = (this.questionResult || []).filter(q => q.question === this.tableDetail2.id && q.sequence === this.tableDetail2.sequence && q.question_format === this.tableDetail2.question_format);
      let obj = [];
      try {
        obj = JSON.parse(result[0]?.answer);
      } catch (e) {
        obj = [];
      }
      const answer = (obj || []).filter(r => r.question_id === question.question_id);
      if (result.length > 0) {
        return this.mainTableForm2(answer[0], i);
      } else {
        return this.mainTableForm2(question, i);
      }
    } else {
      return this.mainTableForm2(question, i);
    }
  }
  mainTableForm(val?, i?) {
    if(!val){
      return;
    }
    
    const options = (this.tableDetail1.language[this.language] ?
      this.tableDetail1.language[this.language].options : this.tableDetail1.options)
      .find((option: any) => option.question_id === val.question_id);
    val.name = options?.name || '';
    val.description = options?.description || '';
    return this.fb.group({
      name: new UntypedFormControl(val?.name || null),
      toolTip: new UntypedFormControl(val?.description || val?.toolTip),
      // tslint:disable-next-line:max-line-length
      unit: new UntypedFormControl((val?.unitType?.id ? val?.unitType?.id === 1 ? (val?.unit || 1) : val?.unitType?.id === 3 ? (val?.unit || 1) : val?.unitType?.id === 4 ? 8 : 1 : val?.unit)),
      inputValue: new UntypedFormControl(val?.inputValue),
      isRequired: new UntypedFormControl(val?.isRequired || false),
      question_id: new UntypedFormControl(val?.question_id),
      pgas: new UntypedFormControl(val?.pgas || null),
      priority: new UntypedFormControl(val?.priority),
      // tslint:disable-next-line:triple-equals
      isExpanded: new UntypedFormControl(false),
      icon: new UntypedFormControl(false),
      // group: new FormControl(val?.group),
      isDisabled: new UntypedFormControl(val?.isDisabled || false),
      supplygroupName: new UntypedFormControl(val?.supplygroupName || []),
      subGroup: new UntypedFormControl(val?.subGroup || [])
    });
  }
  mainTableForm2(val?, i?) {
    const options = (this.tableDetail2.language[this.language] ?
      this.tableDetail2.language[this.language].options : this.tableDetail2.options)
      .find((option: any) => option.question_id === val.question_id);
    val.name = options.name;
    val.description = options.description;

    return this.fb.group({
      name: new UntypedFormControl(val?.name || null),
      toolTip: new UntypedFormControl(val?.description || val?.toolTip),
      unit: new UntypedFormControl((val?.unitType?.id ? val?.unitType?.id === 1 ? (val?.unit || 1) : val?.unitType?.id === 3 ? (val?.unit || 1) : val?.unitType?.id === 4 ? 8 : 1 : val?.unit)),
      inputValue: new UntypedFormControl(val?.inputValue),
      sfld: new UntypedFormControl(val?.sfld),
      sfnd: new UntypedFormControl(val?.sfnd),
      sffd: new UntypedFormControl(val?.sffd),
      total: new UntypedFormControl(val?.total),
      udat: new UntypedFormControl(val?.udat || null),
      local: new UntypedFormControl(val?.local || null),
      near: new UntypedFormControl(val?.near || null),
      far: new UntypedFormControl(val?.far || null),
      totalall: new UntypedFormControl(val?.totalall || null),
      question_id: new UntypedFormControl(val?.question_id),
    });
  }
  getActualIndex(index: number) {
    return index + (this.paginator?.pageSize || 0) * (this.paginator?.pageIndex || 0);
  }
  checkForUnitConversion(targetForm, i, inputValue, table) {
    inputValue = _.cloneDeep(parseFloat(inputValue || 0));
    if (this[table][i]?.unitType?.id === 1 && targetForm.value.unit !== 1) {
      return this.uc.kgConversion(targetForm.value.unit, inputValue) || 0;
    } else if (this[table][i]?.unitType?.id === 3 && targetForm.value.unit !== 1) {
      return this.uc.currencyConversion(targetForm.value.unit, inputValue);
    }
    else {
      return inputValue;
    }
  }
  onValueChange(value, i, whereFrom?) {
    if (whereFrom === 'template') {
      i = this.getActualIndex(i);
    }
    const targetForm = ((this.supplyChainForm.get('Rows') as UntypedFormArray).at(i) as UntypedFormGroup);
    if (this.tableDetails1[i]?.unitType?.id === 4) {
      targetForm.controls.inputValue.clearValidators();
      targetForm.controls.inputValue.updateValueAndValidity();
      const value1 = parseFloat(value);
      if (value1 > 100) {
        targetForm.controls.inputValue.setValidators([percentageValidator(value1, 2)]);
        targetForm.controls.inputValue.updateValueAndValidity();
      }
    }
    const currentRow = this.tableDetails1[i];
    const tonCalLogic = (currentRow?.logic || []).filter(l => l.field.id === 2);
    const autoCalLogic = (currentRow?.logic || []).filter(l => l.field.id === 1);
    const validationLogic = (currentRow?.logic || []).filter(l => l.field.id === 3);
    const toTableLogin = (currentRow?.logic || []).filter(l => l.field.id === 4);
    this.calculateCo2e(targetForm?.value?.inputValue, currentRow, i, tonCalLogic);
    this.checkForcalculations(autoCalLogic, tonCalLogic);
    if (validationLogic.length) {
      this.checkForValidations(validationLogic);
    }
    this.updateTableSupply();
    this.updateSecondTable(toTableLogin);
    this.aggregatePGASValues();
  }
  aggregatePGASValues() {
    const rows = this.supplyChainForm.get('Rows') as UntypedFormArray;

    // Helper to sum child pgas
    const getChildPGASSum = (menuIdList: any[]): number => {
      let sum = 0;
      menuIdList.forEach(child => {
        const index = this.indexReturnFromstring(child.id, 'tableDetails1');
        const formGroup = rows.at(index);
        const value = parseFloat(formGroup.get('pgas')?.value) || 0;
        sum += value;
      });
      return sum;
    };

    // Loop through all rows in reverse to start from Step 3 upward
    for (let i = rows.length - 1; i >= 0; i--) {
      const row = rows.at(i);
      const tableDetail = this.tableDetails1[i];

      // Step 2: Sum step 3 and set to step 2
      if (tableDetail?.step === '2' && tableDetail?.menuId?.length) {
        const totalPGAS = getChildPGASSum(tableDetail.menuId);
        row.patchValue({ pgas: totalPGAS.toFixed(3) });
      }

      // Step 1: Sum step 2 and set to step 1
      if (tableDetail?.step === '1' && tableDetail?.menuId?.length) {
        const totalPGAS = getChildPGASSum(tableDetail.menuId);
        row.patchValue({ pgas: totalPGAS.toFixed(3) });
      }
    }
  }

  onValueChange1(i) {
    this.calculateDistanceEmission(i, 'localCo2e', 'sfld', 'local');
    this.calculateDistanceEmission(i, 'nearCo2e', 'sfnd', 'near');
    this.calculateDistanceEmission(i, 'farCo2e', 'sffd', 'far');
  }
  onUnitChange(i, type) {
    const form = (this.supplyChainForm1.get('Rows') as UntypedFormArray).at(i);
    
    form.patchValue({
      isExpanded: form.value.isExpanded,
      unit: type === 'weight' ? this.volUnit : this.monUnit
    });
    if (form.value.inputValue) {
      this.onValueChange(form.value.inputValue, i);
    }
  }
  onUnitChange1(i, type) {
    const form = (this.supplyChainForm1.get('Rows') as UntypedFormArray).at(i);
    form.patchValue({
      isExpanded: form.value.isExpanded,
      unit: type === 'weight' ? Number(this.volUnit) : this.monUnit
    });

    if (form.value.inputValue) {
      this.onValueChange1(i);
    }
  }
  checkForcalculations(autoCalLogic, tonCalLogic) {
    (autoCalLogic || []).forEach((l, i) => {
      if (l && l.field?.id === 1 && l.type?.name && l.target?.id) {
        const targetIndex = this.indexReturnFromstring(l.target.id, 'tableDetails1');
        const targetRow = this.tableDetails1[targetIndex];
        const targetForm = (this.supplyChainForm.get('Rows') as UntypedFormArray).at(targetIndex);
        const targetValue = targetForm.value?.inputValue &&
          targetForm.value?.inputValue !== null &&
          targetForm.value?.inputValue !== undefined ? targetForm.value?.inputValue : 0;
        if (l.type.name === 'subtract') {
          const fromValue = this.getCurrentFormArrayValue(this.indexReturnFromstring(l.from.id, 'tableDetails1'), 'inputValue', false);
          targetForm.patchValue({
            inputValue: (fromValue - targetValue).toFixed(2),
          });
        }
        if (l.type.name === 'sum') {
          let sum = 0;
          (l.from || []).forEach(s => {
            sum += this.getCurrentFormArrayValue(this.indexReturnFromstring(s.id, 'tableDetails1'), 'inputValue', false);
          });
          targetForm.patchValue({
            inputValue: sum.toFixed(2)
          });
        }
        // this.checkForUnitConversion(targetForm, targetIndex, targetForm.value.inputValue);
        if (i === autoCalLogic.length - 1) {
          this.calculateCo2e(targetForm.value.inputValue, targetRow, targetIndex, tonCalLogic);
        }
      }
    });
  }
  getCurrentFormArrayValue(index, key, checkConv) {
    const form = (this.supplyChainForm.get('Rows') as UntypedFormArray).at(index);      
    let formValue = form.value[key];
    if (key === 'inputValue' && checkConv === true) {
      formValue = this.checkForUnitConversion(form, index, formValue, 'tableDetails1');
    }
    if (formValue !== null && formValue !== undefined && formValue) {
      if (this.tableDetails1[index]?.unitType.id === 4) {
        return parseFloat(formValue) / 100;
      }
      return parseFloat(formValue);
    } else {
      return 0;
    }
  }
  calculateTonCo2e(tonCalLogic) {
    let allSum = 0;
    let diff = 0;
    let diffCal = 0;
    let sumCal = 0;
    (tonCalLogic || []).forEach((l, i) => {
      const targetIndex = this.indexReturnFromstring(l.target.id, 'tableDetails1');
      const targetForm = (this.supplyChainForm.get('Rows') as UntypedFormArray).at(targetIndex);
      const targetRow = this.tableDetails1[targetIndex];
      let targetValue = targetForm.value?.inputValue &&
        targetForm.value?.inputValue !== null &&
        targetForm.value?.inputValue !== undefined ? targetForm.value?.inputValue : 0;
      targetValue = this.checkForUnitConversion(targetForm, targetIndex, targetForm.value.inputValue, 'tableDetails1');
      const emissionType = targetRow.emissionType || {};
      const emissionValue = (this.emissionService.carbonEmissionFactor || []).find(e => e.emission_type === emissionType.id);
      if (l && l.field?.id === 2 && l.type?.name && l.target?.id) {
        // type 1
        if (l.methodType.id === 1) {
          if (l.type.name === 'subtract') {
            const fromValue = this.getCurrentFormArrayValue(this.indexReturnFromstring(l.from.id, 'tableDetails1'), 'inputValue', true);
            diff = fromValue - allSum;
            allSum = 0;
          }
          if (l.type.name === 'sum') {
            (l.from || []).forEach(s => {
              allSum += this.getCurrentFormArrayValue(this.indexReturnFromstring(s.id, 'tableDetails1'), 'inputValue', true);
            });
          }
          if (i === tonCalLogic.length - 1) {
            const cal = (diff * (emissionValue?.value || 0)) / 1000;
            targetForm.patchValue({
              pgas: cal < 0 ? 0 : cal.toFixed(3)
            });
          }
        }
        // only sum
        if (l.methodType.id === 2) {
          if (l.type.name === 'sum') {
            let sum = 0;
            (l.from || []).forEach(s => {
              sum += this.getCurrentFormArrayValue(this.indexReturnFromstring(s.id, 'tableDetails1'), 'pgas', true);
            });
            targetForm.patchValue({
              pgas: sum < 0 ? 0 : sum.toFixed(3)
            });
          }
        }
        // only subtract
        if (l.methodType.id === 3) {
          if (l.type.name === 'subtract') {
            const fromValue = this.getCurrentFormArrayValue(this.indexReturnFromstring(l.from.id, 'tableDetails1'), 'inputValue', true);
            const cal = ((targetValue - fromValue) * (emissionValue?.value || 0)) / 1000;
            targetForm.patchValue({
              pgas: cal < 0 ? 0 : cal.toFixed(3)
            });
          }
        }
        //  type 2
        if (l.methodType.id === 4) {
          if (l.type.name === 'subtract') {
            diffCal = 0;
            const fromValue = this.getCurrentFormArrayValue(this.indexReturnFromstring(l.from.id, 'tableDetails1'), 'inputValue', true);
            diffCal = (targetValue - fromValue);
          }
          if (l.type.name === 'sum') {
            sumCal = 0;
            (l.from || []).forEach(s => {
              sumCal += this.getCurrentFormArrayValue(this.indexReturnFromstring(s.id, 'tableDetails1'), 'inputValue', true);
            });
          }
          const totalCal = (diffCal * (1 - sumCal) * (emissionValue?.value || 0)) / 1000;
          targetForm.patchValue({
            pgas: totalCal < 0 ? 0 : totalCal.toFixed(3)
          });
        }
        // type 3
        if (l.methodType.id === 5) {
          let sumCals = 0;
          if (l.type.name === 'sum') {
            (l.from || []).forEach(s => {
              sumCals += this.getCurrentFormArrayValue(this.indexReturnFromstring(s.id, 'tableDetails1'), 'inputValue', true);
            });
          }
          const cal = (targetForm.value.inputValue * (1 - sumCals) * (emissionValue?.value || 0)) / 1000;
          targetForm.patchValue({
            pgas: cal < 0 ? 0 : cal.toFixed(3)
          });
        }
        // multiplication
        if (l.type.name === 'mutiple') {
          let mul = 1;
          (l.from || []).forEach(s => {
            mul *= this.getCurrentFormArrayValue(this.indexReturnFromstring(s.id, 'tableDetails1'), 'inputValue', true);
          });
          const cal = (mul * (emissionValue?.value || 0)) / 1000;
          targetForm.patchValue({
            pgas: cal < 0 ? 0 : cal.toFixed(3)
          });
        }
      }
    });
  }
  calculateCo2e(value, currentRow, i, tonCalLogic) {
    value = parseFloat(value || 0);
    // tslint:disable-next-line:curly
    const emissionType = currentRow?.emissionType || {};
    const foundEmission = this.emissionService.carbonEmissionFactor?.find(e => e.emission_type === emissionType.id);
    const emissionValue = foundEmission ? _.cloneDeep(foundEmission) : {};
    const myForm = ((this.supplyChainForm.get('Rows') as UntypedFormArray).at(i) as UntypedFormGroup);
    if (myForm.value.isDisabled) {
      myForm.controls.inputValue.clearValidators();
      myForm.controls.inputValue.updateValueAndValidity();
      if (value < 0) {
        myForm.controls.inputValue.setValidators([resultNotLessThanZero(value)]);
        myForm.controls.inputValue.updateValueAndValidity();
        value = 0;
      }
    }
    let convertedValue = value;
    if (this.volUnit !== 1) {
      convertedValue = this.uc.kgConversion(this.volUnit, value);
    }
    const cal = (convertedValue * (emissionValue?.value || 0)) / 1000;
    myForm.patchValue({
      pgas: cal.toFixed(3)
    });
    this.calculateTonCo2e(tonCalLogic);
  }
  calculateDistanceEmission(i, key, formControlname, target) {
    if (true) {
      let distanceValue = 0;
      (this.firstForm.value.Rows || []).forEach((f, ind) => {
        if (f[key] && ind !== 0) {
          distanceValue += (parseFloat(f[key]) || 0);
        }
      });
      const myForm = ((this.supplyChainForm1.get('Rows') as UntypedFormArray).at(i) as UntypedFormGroup);
      const inputValue = this.checkForUnitConversion(myForm, i, myForm.value.inputValue, 'tableDetails2') || 0;
      const val = myForm.value;

      const sum = (val.sfld || 0) + (val.sfnd || 0) + (val.sffd || 0);
      myForm.controls.sfld.clearValidators();
      myForm.controls.sfld.updateValueAndValidity();
      if (sum !== 0 && !(sum === 100)) {
        myForm.controls.sfld.setValidators([percentageValidator(sum, 1)]);
        myForm.controls.sfld.updateValueAndValidity();
      }
      const cal = (inputValue * (myForm.value[formControlname] / 100) * distanceValue) / 1000;

      myForm.patchValue({
        [target]: (cal).toFixed(3)
      });
      const udat = (parseFloat(myForm.value.local) || 0) + (parseFloat(myForm.value.near) || 0) + (parseFloat(myForm.value.far) || 0);
      myForm.patchValue({ udat: udat.toFixed(3) });

      const sffld = parseFloat(myForm.get('sfld')?.value || 0);

      const localCalc = ((inputValue * sffld * this.localCo2eSum) / 1000);

      myForm.patchValue({
        local: localCalc.toFixed(3)
      });

      const sffnd = parseFloat(myForm.get('sfnd')?.value || 0);
      const nearCalc = ((inputValue * sffnd * this.nearCo2eSum) / 1000);

      myForm.patchValue({
        near: nearCalc.toFixed(3)
      });

      const sfffd = parseFloat(myForm.get('sffd')?.value || 0);
      const farCalc = ((inputValue * sfffd * this.farCo2eSum) / 1000);

      myForm.patchValue({
        far: farCalc.toFixed(3)
      });

      const totalall = (parseFloat(myForm.value.local) || 0) + (parseFloat(myForm.value.near) || 0) + (parseFloat(myForm.value.far) || 0);
      myForm.patchValue({
        totalall: totalall.toFixed(3)
      });
    }
  }

  checkForValidation(table, form, condition): boolean {
    const rows = this[table].options;
    this.errorMessageList1 = [];
    rows.map((row: any, id: number) => {
      if (row.isRequired) {
        const rowAnswer = (this[form].get('Rows') as UntypedFormArray).at(id).value;
        const errorObj = { name: rowAnswer.name };
        if (condition) {
          if (rowAnswer.inputValue === null || rowAnswer.inputValue === undefined) {
            errorObj.name = rowAnswer.name + ' (value)';
            this.errorMessageList1.push(errorObj);
          }
        } else {
          if ((rowAnswer.sfld === null || rowAnswer.sfld === undefined)
            || (rowAnswer.sfnd === null || rowAnswer.sfnd === undefined) || (rowAnswer.sffd === null || rowAnswer.sffd === undefined)) {
            errorObj.name = rowAnswer.name + ((rowAnswer.sfld === null || rowAnswer.sfld === undefined) ? ' (local)' : '') +
              ((rowAnswer.sfnd === null || rowAnswer.sfnd === undefined) ? ' (near)' : '') +
              ((rowAnswer.sffd === null || rowAnswer.sffd === undefined) ? ' (far)' : '');
            this.errorMessageList1.push(errorObj);
          }
        }
      }
    });
    if (this.errorMessageList1.length) {
      this.dialogRef = this.modalService.open(this.errorMessage1, { centered: true });
      return true;
    }
    return false;
  }
  selectUnit(e, type) {
    if (type === 'weight') {
      this.tableDetails2.forEach((volume: any, i: number) => {
        if (volume.unitType?.id === 1) {
          this.onUnitChange1(i, type);
        }
      });
      this.recalculateAllFirstTableRows();
      this.recalculateAllNonElectricRows();
      this.recalculateAllPpeRows();
    }
    if (type === 'currency') {
      this.tableDetails1.forEach((volume: any, i: number) => {
        if (volume.unitType.id === 3) {
          this.onUnitChange(i, type);
        }
      });
      this.tableDetails2.forEach((volume: any, i: number) => {
        if (volume.unitType.id === 3) {
          this.onUnitChange1(i, type);
        }
      });
    }
    this.updateTableSupply();
  }
  recalculateAllFirstTableRows() {
  const rows = this.supplyChainForm.get('Rows') as UntypedFormArray;
  rows.controls.forEach((group, i) => {
    const value = group.get('inputValue')?.value ?? 0;
    this.onValueChange(value, i);
  });
}
  recalculateAllPpeRows() {
    const rows = this.ppeForm.get('Rows') as UntypedFormArray;
    rows.controls.forEach((group, i) => {
      const value = group.get('vpfpr').value;
      this.onPpeValueChange(value, i, 'vpfpr');
    });
  }
  recalculateAllNonElectricRows(){
    const rows = this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray;
    rows.controls.forEach((group, i) => {
      const value = group.get('purchasedVolume').value;
      this.onNonElectricValueChange(value, i, 'purchasedVolume');
    });
  }
  selectDistanceUnit(e) {
    this.changesInLFN();
  }
  toggle(value, index, condition, type?) {
    const myForm = (this.supplyChainForm.get('Rows') as UntypedFormArray).at(index);

    myForm.patchValue({
      icon: condition
    });

    const formIndex = this.indexReturnFromstring(value.question_id, 'tableDetails1');
    const tableDe = this.tableDetails1[formIndex];
    if (tableDe.hasOwnProperty('menuId') && tableDe.menuId instanceof Object && tableDe.showExpandButton) {
      const menuId = tableDe.menuId;
      menuId.forEach((menu: any) => {
        const formInnedIndex = this.indexReturnFromstring(menu.id, 'tableDetails1');
        const myForm1 = (this.supplyChainForm.get('Rows') as UntypedFormArray).at(formInnedIndex);
        const steptwoTableDe = this.tableDetails1[formInnedIndex];
        myForm1.patchValue({
          isExpanded: condition
        });
        if (steptwoTableDe?.showExpandButton && steptwoTableDe.step === '2' && condition && !type) {
          this.toggle(myForm1.value, formInnedIndex, true);
        }
      });
    }
    if (!type) {
      this.updateTableSupply();
    }
  }
  openViewMore(tem) {
    this.dialogRef = this.modalService.open(tem, { centered: true });
  }
  close1() {
    this.dialogRef.close();
  }
  checkForValidations(validObjArr) {
    validObjArr.map((obj: any) => {
      const targetIdIndex = this.indexReturnFromstring(obj?.target?.id, 'tableDetails1');
      const targetform = ((this.supplyChainForm.get('Rows') as UntypedFormArray).at(targetIdIndex) as UntypedFormGroup);
      // tslint:disable-next-line:radix
      const targetFormValue = parseFloat(targetform.value.inputValue) || 0;
      let sumvalue = 0;
      if (obj.from.length) {
        obj.from.map((inAr: any) => {
          const innerFromIdIndex = this.indexReturnFromstring(inAr?.id, 'tableDetails1');
          const fromform = (this.supplyChainForm.get('Rows') as UntypedFormArray).at(innerFromIdIndex);
          // tslint:disable-next-line:radix
          sumvalue += parseFloat(fromform.value.inputValue) || 0;
        });
      }
      targetform.controls.inputValue.clearValidators();
      targetform.controls.inputValue.updateValueAndValidity();
      if (sumvalue > targetFormValue) {
        targetform.controls.inputValue.setValidators([valueEqualOrGreater(sumvalue, targetFormValue)]);
        targetform.controls.inputValue.updateValueAndValidity();
      }
    });
  }
  getForm(index) {
    return (this.supplyChainForm.get('Rows') as UntypedFormArray).at(index);
  }
  getForm1(index) {
    return (this.supplyChainForm1.get('Rows') as UntypedFormArray).at(index);
  }
  expandMoreOrLess() {
    (this.supplyChainForm.value.Rows || []).forEach((val, index) => {
      this.toggle(val, index, this.expandMC, 'type');
    });
    this.expandMC = !this.expandMC;
    this.updateTableSupply();
  }
  indexReturnFromstring(questionId: string, table) {
    return this[table].findIndex((details: any) => details.question_id === questionId);
  }
  openPopup(template, condition) {
    if (condition) {
      this.showSecondTable = false;
    } else {
      this.showThirdTable = false;
    }
    this.dialogRef = this.dialog.open(template, {
      width: '90vw',
      maxWidth: '100vw',
      height: '100%',
      maxHeight: '90vh',
      panelClass: 'custom-dialog-container',
      disableClose: true,
      hasBackdrop: true
    });
  }
  popupClose(condition) {
    if (condition) {
      this.showSecondTable = true;
    } else {
      this.showThirdTable = true;
    }
    this.dialogRef.close();
  }
  updateTableSupply() {
    this.dataSource = new MatTableDataSource((this.supplyChainForm.get('Rows') as UntypedFormArray).controls);
  }
  updateTable2Supply() {
    this.dataSource1 = new MatTableDataSource((this.supplyChainForm1.get('Rows') as UntypedFormArray).controls);
  }
  createOrUpdateData() {
    this.errorMessageList1 = [];
    if (this.supplyChainForm.invalid) {
      this.errorMessageList1.push({
        name: 'Please check the table - table 1'
      });
      this.dialogRef = this.modalService.open(this.errorMessage1, { centered: true });
      return;
    }
    if (this.supplyChainForm1.invalid) {
      this.errorMessageList1.push({
        name: 'please check the table - table 2'
      });
      this.dialogRef = this.modalService.open(this.errorMessage1, { centered: true });
      return true;
    }
    if (this.checkForValidation('tableDetail1', 'supplyChainForm', true)) {
      return;
    }
    if (this.checkForValidation('tableDetail2', 'supplyChainForm1', false)) {
      return;
    }
    if (this.checkValidatity('firstForm')) {
      this.dialogRef = this.modalService.open(this.errorMessage, { centered: true });
      return;
    }
    // distance table
    this.firstForm.value.Rows[0].local = this.localf.value.local1;
    this.firstForm.value.Rows[0].near = this.nearForm.value;
    this.firstForm.value.Rows[0].far = this.farForm.value;
    const table1Payload: any = {
      answer: this.firstForm?.value.Rows,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      topic: topicId,
      question: this.tableDetail.id,
      question_format: this.tableDetail.question_format,
      sequence: this.tableDetail.sequence,
      unit: { distance: this.distanceUnit },
      isApprovedBy: true,
      service: "Soft FM"
    };
    if (this.id) {
      table1Payload.id = this.id;
      const table1Result = this.questionResult.find(result => result.question === 7);
      if (table1Result) {
        table1Payload.id = table1Result.id;
      }
    }
    // Emission calculation
    const table2Payload: any = {
      answer: this.supplyChainForm.value.Rows,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      topic: 2,
      question: this.tableDetail1.id,
      question_format: this.tableDetail1.question_format,
      sequence: this.tableDetail1.sequence,
      unit: { weight: this.volUnit, currency: this.monUnit },
      isApprovedBy: true,
      service: "Soft FM"
    };
    if (this.id1) {
      table2Payload.id = this.id1;
      const table2Result = this.questionResult.find(result => result.question === 8);
      if (table2Result) {
        table2Payload.id = table2Result.id;
      }
    }
    // Local, Near, Far, Emission
    const table3Payload: any = {
      answer: this.supplyChainForm1.value.Rows,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      topic: 2,
      question: this.tableDetail2.id,
      question_format: this.tableDetail2.question_format,
      sequence: this.tableDetail2.sequence,
      unit: { weight: this.volUnit, currency: this.monUnit },
      isApprovedBy: true,
      service: "Soft FM"
    };
    if (this.id3) {
      table3Payload.id = this.id3;
    }

    // Add non-electric equipment payload
    const nonElectricPayload: any = {
      answer: this.nonElectricEquipmentForm.value.Rows,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      topic: topicId,
      question: this.nonElectricTableDetail.id,
      question_format: this.nonElectricTableDetail.question_format,
      sequence: this.nonElectricTableDetail.sequence,
      unit: { weight: this.volUnit },
      isApprovedBy: true,
      service: "Soft FM"
    };
    
    if (this.nonElectricQuestionId) {
      nonElectricPayload.id = this.nonElectricQuestionId;
    }

    // Add PPE payload
    const ppePayload: any = {
      answer: this.ppeForm.value.Rows,
      site: this.service.siteId,
      start_date: this.service.startDate,
      end_date: this.service.endDate,
      topic: topicId,
      question: this.ppeTableDetail.id,
      question_format: this.ppeTableDetail.question_format,
      sequence: this.ppeTableDetail.sequence,
      unit: { weight: this.volUnit },
      isApprovedBy: true,
      service: "Soft FM"
    };

    if (this.ppeQuestionId) {
      ppePayload.id = this.ppeQuestionId;
    }

    // Add to bulk payload
    const bulkPayload = [table1Payload, table2Payload, table3Payload,  nonElectricPayload,  ppePayload];
    this.spinner.show();
    const response1$ = this.questionResultService.createBulkQuestions(bulkPayload).subscribe((res: any) => {
      // this.service.isAprroved();
      this.spinner.hide();
      this.toaster.success('Data saved successfully');
      this.dataService.updateReload(true);
      this.route.navigate([`carbon-cleaning/${this.service.siteId}/water`]);
    },
      err => {
        this.spinner.hide();
        this.toaster.error('Failed to load questions');
      });
  }
  updateSecondTable(tableLogics) {
    if (this.supplyChainForm1) {
      tableLogics.map((logic: any) => {
        const targetIndex = this.indexReturnFromstring(logic.target.id, 'tableDetails2');
        const targetForm = ((this.supplyChainForm1.get('Rows') as UntypedFormArray).at(targetIndex) as UntypedFormGroup);
        let finalResult = 0;
        logic.from.map((iterate: any) => {
          const table1Index = this.indexReturnFromstring(iterate.id, 'tableDetails1');
          const fromForm = ((this.supplyChainForm.get('Rows') as UntypedFormArray).at(table1Index) as UntypedFormGroup);
          finalResult += parseFloat(fromForm.value.inputValue) || 0;
        });
        targetForm.patchValue({
          inputValue: finalResult
        });
        this.onValueChange1(targetIndex);
      });
    }
  }
  updateNonElectricAndPPEInputValue(){
    const targetFormNonElectric = ((this.supplyChainForm1.get('Rows') as UntypedFormArray).at(2) as UntypedFormGroup);
    const targetFormPPE = ((this.supplyChainForm1.get('Rows') as UntypedFormArray).at(5) as UntypedFormGroup);

    // Only update the inputValue field without triggering other calculations
    targetFormNonElectric.get('inputValue').setValue(this.volumeOfNonElectricEquipment, { emitEvent: false });
    targetFormPPE.get('inputValue').setValue(this.volumeOfPPE, { emitEvent: false });
  }

  updateNonElectricInputValue(){
    const targetFormNonElectric = ((this.supplyChainForm1.get('Rows') as UntypedFormArray).at(2) as UntypedFormGroup);
    // Only update the inputValue field without triggering other calculations
    targetFormNonElectric.get('inputValue').setValue(this.volumeOfNonElectricEquipment, { emitEvent: false });
  }

  updatePPEInputValue(){
    const targetFormPPE = ((this.supplyChainForm1.get('Rows') as UntypedFormArray).at(5) as UntypedFormGroup);
    // Only update the inputValue field without triggering other calculations
    targetFormPPE.get('inputValue').setValue(this.volumeOfPPE, { emitEvent: false });
  }
  openMessage(){
    if(this.selected == 1){
      return; 
    }
    this.dialogRefareyousure = this.modalService.open(this.areyousure, {
      centered: true,
      windowClass: 'approved-dialog-message',
    });
  }
  openMessageclose(){
    this.dialogRefareyousure.close();
  }
  toggleSidenav() {
    this.sidenavOpened = !this.sidenavOpened;
    this.site.setSidenavState(this.sidenavOpened);
  }

  initializeHygienicProductsForm() {
    this.spinner.show();
    this.hygienicProductsForm = this.fb.group({
      Rows: this.fb.array(this.hygienicProducts.map(product => this.createHygienicProductRow(product)))
    });
    
    this.hygienicDataSource = new MatTableDataSource((this.hygienicProductsForm.get('Rows') as UntypedFormArray).controls);
    this.spinner.hide();
  }


  onHygienicProductValueChange(value: any, index: number) {
    // You can add validation or calculations here if needed
    const targetForm = ((this.hygienicProductsForm.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);
    
    // Optional: Add validation if needed
    if (parseFloat(value) < 0) {
      targetForm.get('vppfrp').setValue(0);
    }
    const currentRow = this.hygienicTableDetail[index] || {};
    const emissionType = currentRow.emissionType || {};
    
    const emissionValue = (this.emissionService.carbonEmissionFactor || []).find(e => e.emission_type === emissionType.id);

    targetForm.get('pgas').setValue(
      ((targetForm.get('vppfrp').value || 0) * (emissionValue?.value || 0) / 1000).toFixed(3)
    );

    this.hygienicProductsForm.value.Rows.forEach(obj => this.volumeOfHydienicProducts += obj.vppfrp || 0);
  }
  initiateHygienicProductsTable() {
    this.spinner.show();
    
    // Find the hygienic products question by sequence number or other identifiable property
    // This avoids using a direct index which is fragile
    const hygienicQuestion = this.questionsList.find(q => 
      q.sequence === 4 && // Assuming hygienic products have sequence 4
      q.question_format === 1 && 
      q.is_active
    );
    
    if (!hygienicQuestion || !hygienicQuestion.options || !hygienicQuestion.options.length) {
      this.spinner.hide();
      return;
    }
    
    this.hygienicTableDetail = hygienicQuestion;
    this.hygienicTableDetail = hygienicQuestion.options || [];
    
    // Find the question result ID if it exists
    if (this.questionResult && this.questionResult.length > 0) {
      const hygienicQuestionResult = this.questionResult.find(result => 
        result.question === hygienicQuestion.id
      );
      
      if (hygienicQuestionResult) {
        this.hygienicQuestionId = hygienicQuestionResult.id;
      }
    }
    
    // Create the form with dynamic data from API
    this.hygienicProductsForm = this.fb.group({
      Rows: this.fb.array((this.hygienicTableDetail || []).map((val, i) => 
        this.createHygienicProductRow(val)
      ))
    });
    
    this.hygienicDataSource = new MatTableDataSource(
      (this.hygienicProductsForm.get('Rows') as UntypedFormArray).controls
    );
    
    this.spinner.hide();
  }
  createHygienicProductRow(option: any) {
    let vppfrp = 0;
    let pgas =0
    // Check if we have existing data for this product
    if (this.questionResult && this.questionResult.length > 0) {
      const hygienicQuestionResult = this.questionResult.find(result => 
        result.question === this.hygienicTableDetail.id
      );
      
      if (hygienicQuestionResult) {
        let answerData;
        try {
          answerData = typeof hygienicQuestionResult.answer === 'string' ? 
            JSON.parse(hygienicQuestionResult.answer) : hygienicQuestionResult.answer;
        } catch (e) {
          answerData = [];
        }
        
        // Find the specific product answer
        const productAnswer = Array.isArray(answerData) ? 
          answerData.find((a: any) => a.question_id === option.question_id) : null;
        
        if (productAnswer) {
          vppfrp = productAnswer.vppfrp || 0;
                    pgas = productAnswer.pgas || 0;

        }
      }
    }
    
    // Get the translated name if available
    const translatedOption = this.hygienicTableDetail.language && this.hygienicTableDetail.language[this.language] ?
      this.hygienicTableDetail.language[this.language].options.find((o: any) => o.question_id === option.question_id) :
      null;
    
    return this.fb.group({
      productId: new UntypedFormControl(option.question_id),
      productName: new UntypedFormControl(translatedOption?.name || option.name),
      vppfrp: new UntypedFormControl(vppfrp),
      pgas:new UntypedFormControl(pgas), 
      question_id: new UntypedFormControl(option.question_id),
      toolTip: new UntypedFormControl(translatedOption?.description || option.description || '')
    });
  }
  initiateNonElectricEquipmentTable() {
    this.spinner.show();
    
    // Find the non-electric equipment question by sequence number
    const nonElectricQuestion = this.questionsList.find(q => 
      q.sequence === 5 && 
      q.question_format === 1 && 
      q.is_active
    );
    
    if (!nonElectricQuestion || !nonElectricQuestion.options || !nonElectricQuestion.options.length) {
      this.spinner.hide();
      return;
    }
    
    this.nonElectricTableDetail = nonElectricQuestion;
    
    // Add step and showExpandButton properties to each option
    this.nonElectricTableDetails = (nonElectricQuestion.options || []).map(option => {
      return {
        ...option,
        step: option.step || '1', // Default to step 1 if not provided
        showExpandButton: option.showExpandButton !== undefined ? option.showExpandButton : true
      };
    });
    
    // Find the question result ID if it exists
    if (this.questionResult && this.questionResult.length > 0) {
      const nonElectricQuestionResult = this.questionResult.find(result => 
        result.question === nonElectricQuestion.id
      );
      
      if (nonElectricQuestionResult) {
        this.nonElectricQuestionId = nonElectricQuestionResult.id;
      }
    }
    
    // Create the form with dynamic data from API
    this.nonElectricEquipmentForm = this.fb.group({
      Rows: this.fb.array((this.nonElectricTableDetails || []).map((val, i) => this.createNonElectricEquipmentRow(val, i)))
    });
    
    this.nonElectricDataSource = new MatTableDataSource(
      (this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).controls
    );
    this.expandMoreOrLessNonElectric();
    this.aggregateNonElectricPGASValues();
    this.spinner.hide();
  }
  createNonElectricEquipmentRow(option: any, i) {
    let purchasedVolume = 0;
    let usedVolume = 0;
    let disposedVolume = 0;
    let pgas =0
    // Check if we have existing data for this equipment
    if (this.questionResult && this.questionResult.length > 0) {
      const nonElectricQuestionResult = this.questionResult.find(result => 
        result.question === this.nonElectricTableDetail.id
      );
      
      if (nonElectricQuestionResult) {
        let answerData;
        try {
          answerData = typeof nonElectricQuestionResult.answer === 'string' ? 
            JSON.parse(nonElectricQuestionResult.answer) : nonElectricQuestionResult.answer;
        } catch (e) {
          answerData = [];
        }
        
        // Find the specific equipment answer
        const equipmentAnswer = Array.isArray(answerData) ? 
          answerData.find((a: any) => a.question_id === option.question_id) : null;
        
        if (equipmentAnswer) {
          purchasedVolume = equipmentAnswer.purchasedVolume || 0;
          usedVolume = equipmentAnswer.usedVolume || 0;
          disposedVolume = equipmentAnswer.disposedVolume || 0;
          pgas = equipmentAnswer.pgas || 0; // <-- ADD THIS LINE
        }
      }
    }
    
    // Get the translated name if available
    const translatedOption = this.nonElectricTableDetail.language && this.nonElectricTableDetail.language[this.language] ?
      this.nonElectricTableDetail.language[this.language].options.find((o: any) => o.question_id === option.question_id) :
      null;
    
    return this.fb.group({
      equipmentId: new UntypedFormControl(option.question_id),
      equipmentName: new UntypedFormControl(translatedOption?.name || option.name),
      // materialType: new UntypedFormControl(option.materialType || ''),
      purchasedVolume: new UntypedFormControl(purchasedVolume),
      usedVolume: new UntypedFormControl(usedVolume),
      pgas: new UntypedFormControl(pgas),
      disposedVolume: new UntypedFormControl(disposedVolume),
      question_id: new UntypedFormControl(option.question_id),
      toolTip: new UntypedFormControl(translatedOption?.description || option.description || ''),
      isExpanded: new UntypedFormControl(false),
      priority: new UntypedFormControl(option.priority ),
      icon: new UntypedFormControl(false) // Set to false initially to make it collapsed
    });
  }
  onNonElectricValueChange(value: any, index: number, field: string) {
    const targetForm = ((this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);
    
    if (parseFloat(value) < 0) {
      targetForm.get(field).setValue(0);
    }

    // Mark form as dirty
    this.nonElectricEquipmentForm.markAsDirty();

    const currentRow = this.nonElectricTableDetails[index];
    
    // Extract logic arrays similar to supply chain
    const tonCalLogic = (currentRow?.logic || []).filter(l => l.field.id === 2);
    const autoCalLogic = (currentRow?.logic || []).filter(l => l.field.id === 1);
    const validationLogic = (currentRow?.logic || []).filter(l => l.field.id === 3);

    // Only calculate CO2e for purchasedVolume changes
    if (field === 'purchasedVolume') {
      this.calculateNonElectricCo2e(targetForm?.value?.purchasedVolume, currentRow, index, tonCalLogic);
    }
    
    // Handle automatic calculations (sum/subtract logic) for the specific field
    this.checkNonElectricCalculations(autoCalLogic, tonCalLogic, field);
    
    // Handle validations if needed
    if (validationLogic.length) {
      this.checkNonElectricValidations(validationLogic);
    }

    // Store only the value from the row where step === '1' in volumeOfNonElectricEquipment
    const step1Row = this.nonElectricEquipmentForm.value.Rows.find((row, idx) => this.nonElectricTableDetails[idx]?.step === '1');
    this.volumeOfNonElectricEquipment = step1Row ? (parseFloat(step1Row.purchasedVolume) || 0) : 0;
    // Call updateNonElectricInputValue to only update non-electric equipment value
    this.updateNonElectricInputValue();
    // Update data source
    this.updateNonElectricDataSource();

    this.aggregateNonElectricPGASValues();
  }
  aggregateNonElectricPGASValues() {
    const rows = this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray;

    // Helper to sum child pgas
    const getChildPGASSum = (menuIdList: any[]): number => {
      let sum = 0;
      menuIdList.forEach(child => {
        const index = this.indexReturnFromstring(child.id, 'nonElectricTableDetails');
        const formGroup = rows.at(index);
        const value = parseFloat(formGroup.get('pgas')?.value) || 0;
        sum += value;
      });
      return sum;
    };

    // Loop through all rows in reverse to start from Step 3 upward
    for (let i = rows.length - 1; i >= 0; i--) {
      const row = rows.at(i);
      const tableDetail = this.nonElectricTableDetails[i];

      // Step 2: Sum step 3 and set to step 2
      if (tableDetail?.step === '2' && tableDetail?.menuId?.length) {
        const totalPGAS = getChildPGASSum(tableDetail.menuId);
        row.patchValue({ pgas: totalPGAS.toFixed(3) });
      }

      // Step 1: Sum step 2 and set to step 1
      if (tableDetail?.step === '1' && tableDetail?.menuId?.length) {
        const totalPGAS = getChildPGASSum(tableDetail.menuId);
        row.patchValue({ pgas: totalPGAS.toFixed(3) });
      }
    }
  }
  calculateNonElectricCo2e(value, currentRow, i, tonCalLogic) {
    // If this is the parent row (step 1), always set pgas to 0.000 and skip calculation
    if (currentRow.step === '1') {
      const myForm = ((this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).at(i) as UntypedFormGroup);
      myForm.patchValue({ pgas: '0.000' });
      return;
    }
    value = parseFloat(value || 0);
    
    const emissionType = currentRow?.emissionType || {};
    const foundEmission = this.emissionService.carbonEmissionFactor?.find(e => e.emission_type === emissionType.id);
    const emissionValue = foundEmission ? _.cloneDeep(foundEmission) : {};
    
    const myForm = ((this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).at(i) as UntypedFormGroup);
    
    // Handle disabled form validation
    if (myForm.value.isDisabled) {
      myForm.controls.purchasedVolume.clearValidators();
      myForm.controls.purchasedVolume.updateValueAndValidity();
      if (value < 0) {
        myForm.controls.purchasedVolume.setValidators([resultNotLessThanZero(value)]);
        myForm.controls.purchasedVolume.updateValueAndValidity();
        value = 0;
      }
    }

    let convertedValue = value;
    if (this.volUnit !== 1) {
      convertedValue = this.uc.kgConversion(this.volUnit, value);
    }
    
    // Calculate CO2e
    const cal = (convertedValue * (emissionValue?.value || 0)) / 1000;
    
    // Patch pgas and mark form as dirty
    myForm.patchValue({
      pgas: cal.toFixed(3)
    });
    myForm.markAsDirty();
    
    
    // Calculate hierarchical totals
    this.calculateNonElectricTonCo2e(tonCalLogic);
  }

  checkNonElectricCalculations(autoCalLogic, tonCalLogic, field = 'purchasedVolume') {
    (autoCalLogic || []).forEach((l, i) => {
      if (l && l.field?.id === 1 && l.type?.name && l.target?.id) {
        const targetIndex = this.indexReturnFromNonElectricString(l.target.id);
        const targetRow = this.nonElectricTableDetails[targetIndex];
        const targetForm = (this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).at(targetIndex);
        
        const targetValue = targetForm.value?.[field] &&
          targetForm.value?.[field] !== null &&
          targetForm.value?.[field] !== undefined ? targetForm.value?.[field] : 0;

        if (l.type.name === 'subtract') {
          const fromValue = this.getNonElectricFormArrayValue(this.indexReturnFromNonElectricString(l.from.id), field, false);
          targetForm.patchValue({
            [field]: (fromValue - targetValue).toFixed(2),
          });
        }

        if (l.type.name === 'sum') {
          let sum = 0;
          (l.from || []).forEach(s => {
            sum += this.getNonElectricFormArrayValue(this.indexReturnFromNonElectricString(s.id), field, false);
          });
          targetForm.patchValue({
            [field]: sum.toFixed(2)
          });
        }

        // Only recalculate CO2e for purchasedVolume changes
        if (i === autoCalLogic.length - 1 && field === 'purchasedVolume') {
          this.calculateNonElectricCo2e(targetForm.value.purchasedVolume, targetRow, targetIndex, tonCalLogic);
        }
      }
    });
  }
  calculateNonElectricTonCo2e(tonCalLogic, field = 'purchasedVolume') {
    let allSum = 0;
    let diff = 0;
    let diffCal = 0;
    let sumCal = 0;

    (tonCalLogic || []).forEach((l, i) => {
      const targetIndex = this.indexReturnFromNonElectricString(l.target.id);
      const targetForm = (this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).at(targetIndex);
      const targetRow = this.nonElectricTableDetails[targetIndex];
      
      let targetValue = targetForm.value?.[field] &&
        targetForm.value?.[field] !== null &&
        targetForm.value?.[field] !== undefined ? targetForm.value?.[field] : 0;
      
      targetValue = this.checkNonElectricUnitConversion(targetForm, targetIndex, targetForm.value[field]);
      
      const emissionType = targetRow.emissionType || {};
      const emissionValue = (this.emissionService.carbonEmissionFactor || []).find(e => e.emission_type === emissionType.id);

      if (l && l.field?.id === 2 && l.type?.name && l.target?.id) {
        // Method Type 1: Sum first, then subtract
        if (l.methodType?.id === 1) {
          if (l.type.name === 'subtract') {
            const fromValue = this.getNonElectricFormArrayValue(this.indexReturnFromNonElectricString(l.from.id), field, true);
            diff = fromValue - allSum;
            allSum = 0;
          }
          if (l.type.name === 'sum') {
            (l.from || []).forEach(s => {
              allSum += this.getNonElectricFormArrayValue(this.indexReturnFromNonElectricString(s.id), field, true);
            });
          }
          // Only update pgas for purchasedVolume field
          if (i === tonCalLogic.length - 1 && field === 'purchasedVolume') {
            const cal = (diff * (emissionValue?.value || 0)) / 1000;
            targetForm.patchValue({
              pgas: cal < 0 ? 0 : cal.toFixed(3)
            });
          }
        }

        // Method Type 2: Only sum
        if (l.methodType?.id === 2) {
          if (l.type.name === 'sum') {
            let sum = 0;
            (l.from || []).forEach(s => {
              sum += this.getNonElectricFormArrayValue(this.indexReturnFromNonElectricString(s.id), 'pgas', true);
            });
            // Only update pgas for purchasedVolume field
            if (field === 'purchasedVolume') {
              targetForm.patchValue({
                pgas: sum < 0 ? 0 : sum.toFixed(3)
              });
            }
          }
        }
      }
    });
  }
  // Helper methods
  indexReturnFromNonElectricString(id: string): number {
    return this.nonElectricTableDetails.findIndex(item => item.system_id === id || item.question_id === id);
  }
  getNonElectricFormArrayValue(index: number, key: string, checkConv: boolean): number {
    const form = (this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).at(index);
    let formValue = form.value[key];
    
    if ((key === 'purchasedVolume' || key === 'usedVolume' || key === 'disposedVolume') && checkConv === true) {
      formValue = this.checkNonElectricUnitConversion(form, index, formValue);
    }
    
    if (formValue !== null && formValue !== undefined && formValue) {
      if (this.nonElectricTableDetails[index]?.unitType?.id === 4) {
        return parseFloat(formValue) / 100;
      }
      return parseFloat(formValue);
    } else {
      return 0;
    }
  }
  checkNonElectricUnitConversion(targetForm, i, inputValue): number {
    inputValue = _.cloneDeep(parseFloat(inputValue || 0));
    if (this.nonElectricTableDetails[i]?.unitType?.id === 1 && targetForm.value.unit !== 1) {
      return this.uc.kgConversion(targetForm.value.unit, inputValue) || 0;
    } else if (this.nonElectricTableDetails[i]?.unitType?.id === 3 && targetForm.value.unit !== 1) {
      return this.uc.currencyConversion(targetForm.value.unit, inputValue);
    } else {
      return inputValue;
    }
  }
  checkNonElectricValidations(validationLogic) {
    // Implement validation logic similar to checkForValidations if needed
  }
  updateNonElectricDataSource() {
    this.nonElectricDataSource = new MatTableDataSource((this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).controls);
  }
  toggleNonElectric(value, index, condition, type?) {

  const targetForm = (this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).at(index);
  targetForm.patchValue({
    icon: condition,
  });

  const formIndex = this.indexReturnFromstring(value.question_id, 'nonElectricTableDetails');
  const equipment = this.nonElectricTableDetails[formIndex];

  if (
    equipment.hasOwnProperty('menuId') &&
    equipment.menuId instanceof Object &&
    equipment.showExpandButton
  ) {

    const menuId = equipment.menuId;
    menuId.forEach((menu: any) => {
      const formInnedIndex = this.indexReturnFromstring(menu.id, 'nonElectricTableDetails');
      const myForm2 = (this.nonElectricEquipmentForm.get('Rows') as UntypedFormArray).at(formInnedIndex);
      const steptwoTableDe = this.nonElectricTableDetails[formInnedIndex];

      myForm2.patchValue({
        isExpanded: condition
      });

      if (steptwoTableDe?.showExpandButton && steptwoTableDe.step === '2' && condition && !type) {
        this.toggleNonElectric(myForm2.value, formInnedIndex, true);
      }
    });
  }

  if (!type) {
    this.updateNonElectricTable();
  }

}
updateNonElectricTable() {
  // Update the data source to reflect changes
  this.updateNonElectricDataSource();
}
  initiateChemicalProductsTable() {
    this.spinner.show();
    
    // Find the chemical products question by sequence number
    const chemicalQuestion = this.questionsList.find(q => 
      q.sequence === 6 && // Chemical products have sequence 6
      q.question_format === 1 && 
      q.is_active
    );
    
    if (!chemicalQuestion || !chemicalQuestion.options || !chemicalQuestion.options.length) {
      this.spinner.hide();
      return;
    }
    
    this.chemicalTableDetail = chemicalQuestion;
    const chemicalOptions = chemicalQuestion.options || [];
    
    // Find the question result ID if it exists
    if (this.questionResult && this.questionResult.length > 0) {
      const chemicalQuestionResult = this.questionResult.find(result => 
        result.question === chemicalQuestion.id
      );
      
      if (chemicalQuestionResult) {
        this.chemicalQuestionId = chemicalQuestionResult.id;
      }
    }
    
    // Create the form with dynamic data from API
    this.chemicalProductsForm = this.fb.group({
      Rows: this.fb.array((chemicalOptions || []).map((val, i) => 
        this.createChemicalProductRow(val)
      ))
    });
    
    this.chemicalDataSource = new MatTableDataSource(
      (this.chemicalProductsForm.get('Rows') as UntypedFormArray).controls
    );
    
    this.spinner.hide();
  }

  createChemicalProductRow(option: any) {
    let volume = 0;
    let pgas = 0;
    // Check if we have existing data for this product
    if (this.questionResult && this.questionResult.length > 0) {
      const chemicalQuestionResult = this.questionResult.find(result => 
        result.question === this.chemicalTableDetail.id
      );
      
      if (chemicalQuestionResult) {
        let answerData;
        try {
          answerData = typeof chemicalQuestionResult.answer === 'string' ? 
            JSON.parse(chemicalQuestionResult.answer) : chemicalQuestionResult.answer;
        } catch (e) {
          answerData = [];
        }
        
        // Find the specific product answer
        const productAnswer = Array.isArray(answerData) ? 
          answerData.find((a: any) => a.question_id === option.question_id) : null;
        
        if (productAnswer) {
          volume = productAnswer.volume || 0;
                    pgas = productAnswer.pgas || 0;

        }
      }
    }
    
    // Get the translated name if available
    const translatedOption = this.chemicalTableDetail.language && this.chemicalTableDetail.language[this.language] ?
      this.chemicalTableDetail.language[this.language].options.find((o: any) => o.question_id === option.question_id) :
      null;
    
    return this.fb.group({
      productId: new UntypedFormControl(option.question_id),
      pucs: new UntypedFormControl(translatedOption?.name || option.name),
      ppfrp: new UntypedFormControl(volume),
      pgas:new UntypedFormControl(pgas),
      question_id: new UntypedFormControl(option.question_id),
      toolTip: new UntypedFormControl(translatedOption?.description || option.description || '')
    });
  }

  onChemicalProductValueChange(value: any, index: number) {
    const targetForm = ((this.chemicalProductsForm.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);
    if (parseFloat(value) < 0) {
      targetForm.get('ppfrp').setValue(0);
    }
    
    // Mark the form as dirty to enable save button
    this.chemicalProductsForm.markAsDirty();
    
    // Update the data source if needed
    this.updateChemicalDataSource();

    const currentRow = this.chemicalTableDetail?.options[index] || {};
    const emissionType = currentRow.emissionType || {};
    const emissionValue = (this.emissionService.carbonEmissionFactor || []).find(e => e.emission_type === emissionType.id);

    const convertedValue = this.checkForUnitConversion(targetForm, index, value, 'chemicalTableDetail');
    targetForm.get('pgas').setValue(
      ((convertedValue || 0) * (emissionValue?.value || 0) / 1000).toFixed(3)
    );
  }

  updateChemicalDataSource() {
    this.chemicalDataSource = new MatTableDataSource(
      (this.chemicalProductsForm.get('Rows') as UntypedFormArray).controls
    );
  }

  // Initialize the PPE table
  initiatePpeTable() {
    this.spinner.show();
    
    // Find the PPE question by sequence number
    const ppeQuestion = this.questionsList.find(q => 
      q.sequence === 7 && // PPE items have sequence 7
      q.question_format === 1 && 
      q.is_active
    );
    
    if (!ppeQuestion || !ppeQuestion.options || !ppeQuestion.options.length) {
      this.spinner.hide();
      return;
    }
    
    this.ppeTableDetail = ppeQuestion;
    this.ppeTableDetails = ppeQuestion.options || [];
    
    // Find the question result ID if it exists
    if (this.questionResult && this.questionResult.length > 0) {
      const ppeQuestionResult = this.questionResult.find(result => 
        result.question === ppeQuestion.id
      );
      
      if (ppeQuestionResult) {
        this.ppeQuestionId = ppeQuestionResult.id;
      }
    }
    
    // Create the form with dynamic data from API
    this.ppeForm = this.fb.group({
      Rows: this.fb.array((this.ppeTableDetails || []).map((val, i) => 
        this.createPpeRow(val)
      ))
    });
    
    this.ppeDataSource = new MatTableDataSource(
      (this.ppeForm.get('Rows') as UntypedFormArray).controls
    );
    
    this.spinner.hide();
  }

  // Create a row for each PPE item
  createPpeRow(option: any) {
    let vpfpr = 0;
    let vdrp = 0;
    let pgas = 0;
    // Check if we have existing data for this PPE
    if (this.questionResult && this.questionResult.length > 0) {
      const ppeQuestionResult = this.questionResult.find(result => 
        result.question === this.ppeTableDetail.id
      );
      
      if (ppeQuestionResult) {
        let answerData;
        try {
          answerData = typeof ppeQuestionResult.answer === 'string' ? 
            JSON.parse(ppeQuestionResult.answer) : ppeQuestionResult.answer;
        } catch (e) {
          answerData = [];
        }
        
        // Find the specific PPE answer
        const ppeAnswer = Array.isArray(answerData) ? 
          answerData.find((a: any) => a.question_id === option.question_id) : null;
        
        if (ppeAnswer) {
          vpfpr = ppeAnswer.vpfpr || 0;
          vdrp = ppeAnswer.vdrp || 0;
          pgas = ppeAnswer.pgas || 0;

        }
      }
    }
    
    // Get the translated name if available
    const translatedOption = this.ppeTableDetail.language && this.ppeTableDetail.language[this.language] ?
      this.ppeTableDetail.language[this.language].options.find((o: any) => o.question_id === option.question_id) :
      null;
    
    return this.fb.group({
      ppeId: new UntypedFormControl(option.question_id),
      ppeName: new UntypedFormControl(translatedOption?.name || option.name),
      vpfpr: new UntypedFormControl(vpfpr),
      vdrp: new UntypedFormControl(vdrp),
      pgas: new UntypedFormControl(pgas),
      question_id: new UntypedFormControl(option.question_id),
      toolTip: new UntypedFormControl(translatedOption?.description || option.description || ''),
      tco2e: new UntypedFormControl(option.tco2e || 0)
    });
  }

  // Handle value changes for PPE
  onPpeValueChange(value: any, index: number, field: string) {
    const targetForm = ((this.ppeForm.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);
  
    if (parseFloat(value) < 0) {
      targetForm.get(field).setValue(0);
    }
    
    // Mark the form as dirty to enable save button
    this.ppeForm.markAsDirty();
    
    // Update the data source if needed
    this.updatePpeDataSource();

    const currentRow = this.ppeTableDetail?.options[index] || {};
    const emissionType = currentRow.emissionType || {};
    const emissionValue = (this.emissionService.carbonEmissionFactor || []).find(e => e.emission_type === emissionType.id);

    let convertedValue = value;
    if (this.volUnit !== 1) {
      convertedValue = this.uc.kgConversion(this.volUnit, value);
    }
    targetForm.get('pgas').setValue(
      ((convertedValue || 0) * (emissionValue?.value || 0) / 1000).toFixed(3)
    );

    // Sum all vpfpr values and store in volumeOfPPE
    this.volumeOfPPE = this.ppeForm.value.Rows.reduce((sum, row) => sum + (parseFloat(row.vpfpr) || 0), 0);
    // Call updatePPEInputValue to only update PPE value
    this.updatePPEInputValue();
  }

  // Update the PPE data source
  updatePpeDataSource() {
    this.ppeDataSource = new MatTableDataSource(
      (this.ppeForm.get('Rows') as UntypedFormArray).controls
    );
  }

  expandMoreOrLessNonElectric() {
    (this.nonElectricEquipmentForm.value.Rows || []).forEach((val, index) => {
      this.toggleNonElectric(val, index, !this.expandNonElectric, 'type');
    });
    this.expandNonElectric = !this.expandNonElectric;
    this.updateNonElectricTable();
  }
}
