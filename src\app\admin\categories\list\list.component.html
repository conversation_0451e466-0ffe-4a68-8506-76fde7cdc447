<div class="container-fluid" *ngIf="loading; else content">
    <ngx-spinner 
        type="ball-fussion" 
        bdColor="rgba(255, 255, 255, 0.8)" 
        size="small" 
        color="rgb(42, 41, 92)"
        [fullScreen]="false">
        <p class="text-primary font-weight-bold">Loadingpleasewait...</p>
    </ngx-spinner>
</div>

<ng-template #content>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 form-group">
                <h4 class="text-primary font-weight-bold m-0" style="font-size: 18px;">Topics</h4>
            </div>
            <div class="col-md-12 form-group">
                <div class="row">
                    <div class="col-md-4">
                        <app-category-list  (levelSelected)="levelData($event, 'zero')"
                                            [categoryData]="categoriesLevelZero"
                                            (levelChanged)="updateLevelslist($event, 'zero')"
                                            (levelAdded)="add($event, 0, categoriesLevelZero.length)"
                                            (levelEdited)="add($event, 0)"
                                            (levelDeleted)="updateDeletedlevels($event, 'zero')">
                        </app-category-list>
                    </div>
                    <div class="col-md-4" *ngIf="levelOne">
                        <app-category-list (levelSelected)="levelData($event, 'one')"
                                            [categoryData]="categoriesLevelOne"
                                            [parent]="{ parentId : levelOne, level : 1}"
                                            (levelChanged)="updateLevelslist($event, 'one')"
                                            (levelAdded)="add($event, 1, categoriesLevelOne.length, { parentId : levelOne, level : 1 })"
                                            (levelEdited)="add($event, 1)"
                                            (levelDeleted)="updateDeletedlevels($event, 'one')">
                        </app-category-list>
                    </div>
                    <div class="col-md-4" *ngIf="levelTwo">
                        <app-category-list (levelSelected)="levelData($event, 'two')"
                                            [categoryData]="categoriesLevelTwo"
                                            [parent]="{ parentId : levelTwo, level : 2}"
                                            (levelChanged)="updateLevelslist($event, 'two')"
                                            (levelAdded)="add($event, 2, categoriesLevelTwo.length, { parentId : levelTwo, level : 2 })"
                                            (levelEdited)="add($event, 2)"
                                            (levelDeleted)="updateDeletedlevels($event, 'two')">
                        </app-category-list>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>
