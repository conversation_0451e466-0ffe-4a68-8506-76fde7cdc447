import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CategoriesRoutingModule } from './categories-routing.module';
import { ListComponent } from './list/list.component';
import { AddComponent } from './add/add.component';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';
import { SelectDropDownModule } from 'ngx-select-dropdown';
import { ColorPickerModule } from 'ngx-color-picker';
// import { UiSwitchModule } from 'ngx-toggle-switch';
import { NgxSpinnerModule } from 'ngx-spinner';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
@NgModule({
    declarations: [ListComponent, AddComponent],
    imports: [
        CommonModule,
        CategoriesRoutingModule,
        NgbModule,
        ReactiveFormsModule,
        SharedModule,
        SelectDropDownModule,
        ColorPickerModule,
        // UiSwitchModule,
        NgxSpinnerModule,
        CKEditorModule,
        MatSlideToggleModule,
        TranslateModule
    ]
})
export class CategoriesModule { }
