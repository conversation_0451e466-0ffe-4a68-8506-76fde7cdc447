import { BestPracticeService } from './../../../shared/services/best-practices.service';
import { Component, OnInit, OnDestroy, ViewChild, ElementRef, TemplateRef, ChangeDetectorRef } from '@angular/core';
import { UntypedFormBuilder, Validators, UntypedFormGroup, UntypedFormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgxSpinnerService } from 'ngx-spinner';
import { Observable, Subject, merge, Subscription, combineLatest } from 'rxjs';
import { Practice } from 'src/app/shared/models/practice.model';
import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { CategoryService } from 'src/app/shared/services/category.service';
import { FileUploadService } from 'src/app/shared/services/file-upload.service';
import { debounceTime, distinctUntilChanged, map, filter } from 'rxjs/operators';
import { DomSanitizer } from '@angular/platform-browser';
import { Ng2ImgMaxService } from 'ng2-img-max';
import { MatDialog } from '@angular/material/dialog';
import { ColumnMode } from '@swimlane/ngx-datatable';

@Component({
  selector: 'app-upsert',
  templateUrl: './upsert.component.html',
  styleUrls: ['./upsert.component.scss']
})
export class UpsertComponent implements OnInit, OnDestroy {
  practiceForm: UntypedFormGroup;
  initiativeForm: UntypedFormGroup;
  initiativeData: any = [];
  limit = 10;
  offset = 0;
  count = 0;
  readonly headerHeight = 50;
  readonly footerHeight = 50;
  readonly rowHeight = 50;
  readonly pageLimit = 25;
  offsetPaginate = 0;
  ColumnMode = ColumnMode;
  country: string;
  showTo; ast = true;
  id: number;
  topicsList: any;
  toggleName: boolean;
  selectedFiles: any;
  filesData = [];
  pageOffset: any;
  submitted = false;
  reqLabel = false;
  languageResolve: [];
  expertsType = ['Topic', 'Support'];
  profilePreview: any;
  noPreview = 'assets/logo/no-image.png';
  fileName: string;
  language: UntypedFormArray;
  subscription: Subscription = new Subscription();
  @ViewChild('instance', { static: true }) instance: NgbTypeahead;
  @ViewChild('instance', { static: true }) instanceTopics: NgbTypeahead;
  @ViewChild('topicElement', { static: true }) topicElement: ElementRef;
  @ViewChild('Initiative') Initiative: TemplateRef<any>;
  focusCountry$ = new Subject<string>();
  selectCountry$ = new Subject<string>();
  focusTopics$ = new Subject<string>();
  selectTopics$ = new Subject<string>();
  uploadedImage: Blob;
  countries = [
    'Global',
    'Afghanistan',
    'Albania',
    'Algeria',
    'Antigua and Barbuda',
    'Argentina',
    'Armenia ',
    'Australia',
    'Austria',
    'Azerbaijan',
    'Azores',
    'Bahamas',
    'Bangladesh',
    'Barbados',
    'Belarus',
    'Belgium',
    'Belize',
    'Bermuda',
    'Bolivia',
    'Bosnia & Herzegovina',
    'Brazil',
    'Bulgaria',
    'Cambodia',
    'Cameroon',
    'Canada',
    'Cape Verde',
    'Chile',
    'China',
    'Columbia',
    'Costa Rica',
    'Croatia',
    'Cuba',
    'Cyprus',
    'Czech Republic',
    'Czechoslovakia',
    'Denmark',
    'Dominica',
    'Dominican Republic',
    'Ecuador',
    'Egypt',
    'El Salvador',
    'England',
    'Eritrea',
    'Ethiopia',
    'Fiji',
    'Finland',
    'France',
    'Georgia',
    'Germany',
    'Ghana',
    'Greece',
    'Grenada',
    'Guam',
    'Guatemala',
    'Guyana',
    'Haiti',
    'Honduras',
    'Hong Kong',
    'Hungary',
    'India',
    'Indonesia',
    'Iran',
    'Iraq',
    'Ireland',
    'Israel',
    'Italy',
    'Jamaica',
    'Japan',
    'Jordan',
    'Kenya',
    'Korea',
    'Kosovo',
    'Kuwait',
    'Laos',
    'Latvia',
    'Lebanon',
    'Liberia',
    'Lithuania',
    'Luxembourg',
    'Macedonia',
    'Malaysia',
    'Mexico',
    'Moldova',
    'Morocco',
    'Myanmar(Burma)',
    'Nepal',
    'Netherlands',
    'New Zealand',
    'Nicaragua',
    'Nigeria',
    'Northern Ireland',
    'Norway',
    'Other U.S.Island Areas',
    'Oman',
    'Pakistan',
    'Panama',
    'Paraguay',
    'Peru',
    'Philippines',
    'Poland',
    'Portugal',
    'Qatar',
    'Puerto Rico',
    'Romania',
    'Russia',
    'Samoa',
    'Saudi Arabia',
    'Scotland',
    'Senegal',
    'Serbia',
    'Sierra Leon ',
    'Singapore',
    'Slovakia',
    'Somalia',
    'South Africa',
    'South Korea',
    'Spain',
    'Sri Lanka',
    'St.Kitts--Nevis',
    'St.Lucia',
    'St.Vincent and the Grenadines',
    'Sudan',
    'Sweden',
    'Switzerland',
    'Syria',
    'Taiwan',
    'Tanzania',
    'Thailand',
    'Tonga',
    'Trinidad and Tobago',
    'Turkey',
    'U.S.Virgin Islands',
    'UAE',
    'Uganda',
    'Ukraine',
    'United Kingdom',
    'United States',
    'Uruguay',
    'USSR',
    'Uzbekistan',
    'Venezuela',
    'Vietnam',
    'Wales',
    'Yemen',
    'Yugoslavia',
    'Zimbabwe'
  ];
  selectedImages = [];
  nonWhitespaceRegExp = new RegExp('\\S');
  breadCumsData: any;
  listArray: any;
  newList: any;
  categoryList: any;
  categoryId: any;
  currentCategory: string;
  searchText: any;
  list: any[];
  config = {
    search: true,
    height: '400px',
    placeholder: 'Select the Country'
  };
  initiativeconfig = {
    search: true,
    height: '400px',
    placeholder: 'Select the Initiative Name',
    displayKey: 'initiativename',
  };
  topicConfig = {
    search: true,
    height: '400px',
    placeholder: 'Select the Topic',
    displayKey: 'name',
  };
  isSubAdmin = false;
  dialogInitiative: any;
  initiativeId = [];
  initiativeName = true;
  totalNames: any = [];
  hideInitiativeTable = false;
  constructor(
    private fb: UntypedFormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private spinner: NgxSpinnerService,
    private toaster: ToastrService,
    private practiceService: BestPracticeService,
    private sanitizer: DomSanitizer,
    private ng2ImgMax: Ng2ImgMaxService,
    private fileUploadService: FileUploadService,
    private topicService: CategoryService,
    private dialog: MatDialog,
  ) { }

  ngOnInit() {
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.isSubAdmin = true;
    }
    // this.getLanguages();
    this.languageResolve = this.route.snapshot.data.language;
    this.getCategoryList();
    this.getTopics();
    this.createForm();
    this.pageOffset = this.route.snapshot.queryParams.offset;
    this.profilePreview = 'assets/logo/avatar.png';
    if (this.route.snapshot.params.id) {
      this.toggleName = true;
      this.id = this.route.snapshot.params.id;
      
      this.patchForm();
    }
    this.breadCumsData = {
      limit: 10,
      offset: this.pageOffset,
      search: '',
      sortName: 'createdAt',
      sortValue: 'desc'
    };
  }

  createForm() {
    this.practiceForm = this.fb.group({
      name: ['', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]],
      score: '',
      country: ['', [Validators.required]],
      topic: ['', [Validators.required]],
      imageUrl: ['', [Validators.required]],
      bestPracticeId: ['', [Validators.required]],
      language: this.createLanguageForm(this.languageResolve),
      initiativeId: [''],
      description: ['', [Validators.required]],
    });
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.practiceForm.patchValue({
        country: sessionStorage.getItem('country'),
      });
    }
  }
  createLanguageForm(languages: any[]) {
    const languageControls = {};
    languages.forEach(item => {
      languageControls[item.identifier] = ['', [Validators.minLength(2), Validators.maxLength(100),
      Validators.pattern(this.nonWhitespaceRegExp)]];
    });
    return this.fb.group(languageControls);
  }

  getTopics() {
    this.spinner.show();
    const topicList$ = this.topicService.getRecord('level=2&limit=-1').subscribe((res: any) => {
      this.topicsList = res;
      this.spinner.hide();
    }, (error) => {
      this.spinner.hide();
      throw error;
    });
    this.subscription.add(topicList$);
  }
  getCategoryList() {
    this.spinner.show();
    const categories$ = this.topicService.getCategoryList().subscribe((data) => {
      this.categoryList = data;
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      throw error;
    });
    this.subscription.add(categories$);
  }

  get f() {
    return this.practiceForm.controls;
  }

  countryBasedInitiative(val: any) {
    this.totalNames = [];
    this.getIntiativeList(val.value);
    // this.initiativeList = 
  }

  // Edit and Populating Segments
  patchForm() {
    this.spinner.show();
    this.hideInitiativeTable = true;
    const export$ = this.practiceService.getRecordById(this.id, 'populate=topic,initiative').subscribe((res: any) => {            
      res.initiative.forEach((val:any)=> 
        {
          this.initiativeId.push(val.id);
        }
      );
      this.getIntiativeList(res.country);
      if (res.initiative) {
        this.initiativeData = res.initiative;
        this.count = this.initiativeData.length;
      }
      if (res.initiative) {
        this.initiativeName = false;
      } else {
        this.initiativeName = true;
      }
      // this.checkExpertType(res);
      this.fileName = res.imageUrl;
      this.practiceForm.patchValue({
        name: res.name,
        score: res.score,
        country: res.country,
        imageUrl: this.fileName,
        topic: res.topic ? res.topic.name : '',
        language: res.language || {},
        bestPracticeId: res.bestPracticeId,
        initiativeId: res.initiative || '',
        description: res.description || ''
      });
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      this.listRecord();
      throw error;
    });
    this.subscription.add(export$);
  }

  onTypeChange(val) {
    this.checkExpertType(val);
  }

  checkExpertType(position) {
    if (position === 'Topic') {
      this.reqLabel = true;
      this.practiceForm.get('topic').setValidators([Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]);
      this.practiceForm.get('topic').updateValueAndValidity();
      this.topicElement.nativeElement.disabled = false;
    } else {
      this.practiceForm.get('topic').clearValidators();
      this.practiceForm.get('topic').updateValueAndValidity();
      this.topicElement.nativeElement.value = '';
      this.topicElement.nativeElement.disabled = true;
      this.topicElement.nativeElement.classList.remove('is-invalid');
      this.reqLabel = false;
    }
  }

  fileChange(event) {
    if (event.target.files.length > 0) {
      this.selectedFiles = event.target.files[0];
      const ext = this.selectedFiles.name.split('.').pop();
      if (ext === 'pdf' || ext === 'docx' || ext === 'doc' || ext === 'xls' || ext === 'xlsx') {
        this.filesData[0] = { name: 'document', value: this.selectedFiles };
        this.fileName = this.selectedFiles.name;
      } else {
        // const images$ = this.ng2ImgMax.resizeImage(this.selectedFiles, 400, 300).subscribe(
        //   result => {
        //     this.uploadedImage = result;
        //   },
        //   error => {
        //   }
        // );
        // this.subscription.add(images$);
        this.filesData[1] = { name: 'image', value: this.selectedFiles };
        const reader = new FileReader();
        reader.readAsDataURL(this.selectedFiles);
        reader.onload = (res: any) => {
          this.profilePreview = res.target.result;
          this.profilePreview = this.sanitizer.bypassSecurityTrustUrl(this.profilePreview);
        };
      }
    }
  }

  uploadFile(data, payload) {
    data.forEach(items => {
      const formData = new FormData();
      formData.append('file', items.value);
      const photoObject = this.fileUploadService.uploadFile(formData).pipe(map((eve: any) => {
        return { name: items.name, url: eve.url };
      }));
      this.selectedImages.push(photoObject);
    });

    const photos$ = combineLatest(this.selectedImages).subscribe((val: any) => {
      if (val.length > 0) {
        const resDoc: any = val.find((item: any) => item.name === 'document');
        const resImg: any = val.find((item: any) => item.name === 'image');
        payload.imageUrl = resDoc ? resDoc.url : payload.imageUrl;
        payload.imageUrl = resImg ? resImg.url : payload.imageUrl;
      }
      if (this.id) {
        this.updatePractice(payload);
      } else {
        this.addPractice(payload);
      }
    });
    this.subscription.add(photos$);
  }

  // Adding a Expert
  submit() {
    this.submitted = true;
    if (this.practiceForm.invalid) {
      return;
    } else {
      const payload = this.practiceForm.getRawValue();
      payload.language.en = this.practiceForm.value.name;
      payload.initiative = this.practiceForm.value.initiativeId.length ? this.practiceForm.value.initiativeId.map((i: any) => i.id) : [],
        payload.topic = this.categoryId;
      this.spinner.show();
      if (this.id) {
        if (this.filesData.length) {
          this.uploadFile(this.filesData, payload);
        } else {
          this.updatePractice(payload);
        }
      } else {
        if (this.filesData.length) {
          this.uploadFile(this.filesData, payload);
        } else {
          this.addPractice(payload);
        }
      }

    }
  }

  // Country Look Up
  resultLookUp(value: any) {
    return value;
  }

  inputLookUp(value: any) {
    if (value) {
      return value;
    }
    return value;
  }

  searchExpertsCountry = (text$: Observable<string>) => {
    const deBouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.selectCountry$.pipe(filter(() => !this.instance.isPopupOpen()));
    const inputFocus$ = this.focusCountry$;
    return merge(deBouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.countries.filter(item =>
          // tslint:disable-next-line: max-line-length
          item.toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  // Service LookUp
  resultTopics(value: any) {
    return value.name;
  }

  inputTopics(value: any) {
    if (value.id) {
      return value.name;
    }
    return value;
  }

  searchTopics = (text$: Observable<string>) => {
    const deBouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.selectTopics$.pipe(filter(() => !this.instanceTopics.isPopupOpen()));
    const inputFocus$ = this.focusTopics$;
    return merge(deBouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.topicsList.filter(item =>
          // tslint:disable-next-line: max-line-length
          item.name.toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  // Update Service
  updatePractice(payload) {
    const export$ = this.practiceService.updateRecord(this.id, payload).subscribe((res: Practice) => {
      this.toaster.success('Best Practices updated successfully');
      this.listRecord();
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      if (error.error.data.message === 'Would violate uniqueness constraint-- a record already exists with conflicting value(s).') {
        this.toaster.error('Best Practices already exists for this Topic');
        // throw error;
      } else {
        throw error;
      }
    });
    this.subscription.add(export$);
  }

  addPractice(payload) {
    // payload.imageUrl = this.practiceForm.controls.imageUrl.value.name;
    // payload.imageUrl = payload;

    // this.list = [];
    // this.list.push(payload);
    this.list = payload;
    // this.practiceService.practiceData = this.list;
    const export$ = this.practiceService.createRecord(payload).subscribe((res: Practice) => {
      // this.router.navigate(['admin/practice-guides'], {
      //   queryParams: this.breadCumsData
      // });
      this.toaster.success('Best Practice created successfully'); /*  */
      this.listRecord();
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      if (error.error.data.message === 'Would violate uniqueness constraint-- a record already exists with conflicting value(s).') {
        this.toaster.error(' Best Practice Already Exists for this Topic');
        // throw error;
      } else {
        throw error;
      }
    });
    this.subscription.add(export$);
  }
  getQuestionL3(lev0, lev1, category) {
    this.categoryId = category.id;
    this.currentCategory = category.name;
    this.practiceForm.patchValue({
      topic: category.name
    });
  }
  filterFunction(event) {
    debounceTime(350);
    const div = document.getElementById('myDropdown');
    const a = div.getElementsByTagName('a');
    const SearchText = event.toUpperCase();
    // tslint:disable-next-line: forin
    for (const i in a) {
      const textValue = a[i].textContent || a[i].innerText;
      if (textValue.toUpperCase().indexOf(SearchText) > -1) {
        a[i].style.display = '';
      } else {
        a[i].style.display = 'none';
      }
    }
  }

  close() {
    this.listRecord();
  }

  listRecord() {
    this.router.navigate(['admin/bestpractice'], {
      queryParams: this.breadCumsData
    });
  }

  trackByFn(index) {
    return index;
  }
  preventWhiteSpaces(e) {
    if (e.which === 32 && e.target.selectionStart === 0) {
      e.preventDefault();
    }
  }


  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
  // getInitiative() {
  //   this.practiceService.getIntiativeData().subscribe((res: any) => {
  //     this.initiativeData = res;
  //     this.count = res.length
  //   })
  // }

  getIntiativeList(country: any) {
    this.practiceService.getIntiativeListCountry().subscribe((res: any) => {

      res.forEach((item: any) => {
          item.country.forEach((value)=>{
            if(value.name == country && !this.initiativeId.includes(item.id)){
              this.totalNames.push(item)
            }
          })
      })
      this.totalNames = [...this.totalNames];     
    });

  }
}

