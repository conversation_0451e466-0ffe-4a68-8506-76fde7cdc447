<div class="container-fluid">
    <div class="row justify-content-between">
        <div class="col-md-3 align-self-center">
            <h5 class="text-primary font-weight-bold m-0" style="font-size: 18px;">Experts</h5>
        </div>
        <div class="col-md-9 align-self-center">
            <div class="row justify-content-end">
                <div *ngIf="updateIDs.length" class="col-md-5 d-flex justify-content-end">
                    <button class="btn btn-primary add-button btn-sm py-0 text-white"
                        (click)="confirm('activate')">Activate</button>
                    <button class="btn btn-danger add-button btn-sm py-0 ml-3 text-white"
                        (click)="confirm('deactivate')">Deactivate</button>
                </div>
                <div class="col-md-4 d-flex justify-content-end" aria-describedby="helperText">
                    <input class="form-control py-2" type="text" name="searchField" id="searchField"
                        placeholder="Search by Expert Name" [(ngModel)]="searchText" (keydown)="onInputChange($event)">
                    <small *ngIf="showHelperText" id="helperText" class="form-text text-muted pl-1">
                        Please Enter more than 2 characters to search
                    </small>
                </div>
                <div class="col-md-3 d-flex justify-content-end">
                    <button placement="bottom" ngbTooltip="Add Expert"
                        class="btn btn-primary text-white form-control add-button" (click)="add()" type="submit"><i
                            class="fas fa-plus d-inline fa-sm mr-1"></i><span class="custom-add">Add
                            Expert</span></button>
                    <!-- <button placement="bottom" ngbTooltip="Import"
                        class="btn btn-primary btn-sm text-white form-control add-expert p-0 mx-2"
                        (click)="bulkImportModal('expert')" type="submit"><i
                            class="fa fa-upload d-inline fa-sm mr-1"></i><span class="custom-add">Import</span></button> -->
                </div>
            </div>
        </div>
    </div>

    <div class="row form-group mt-3">
        <div class="col-md-12">
            <ngx-datatable class="bootstrap" [rows]="experts" [columns]="columns" [loadingIndicator]="false"
                [limit]="limit" [offset]="offsetPaginate" [count]="totalCount" [externalPaging]="true"
                (page)="paginate($event)" [externalSorting]="true" [sorts]="[{prop: 'firstName', dir: 'asc'},{prop: 'lastName', dir: 'asc'},{prop: 'isActive', dir: 'asc'}
               ,{prop: 'createdAt', dir: 'desc'}, {prop: 'updatedAt', dir: 'asc'}, {prop: 'isDeleted', dir: 'asc'}]"
                (sort)="sort($event)" [columnMode]="ColumnMode.force" [headerHeight]="50" [footerHeight]="50"
                rowHeight="auto" #dataTable>
            </ngx-datatable>
            <ng-template class="mx-2" #checkboxTemplate let-row="row" let-value="value">
                <input #checkBox type="checkbox" name="check" class="mx-2" id={{value}}
                    (click)="CheckRecords(checkBox,row)">
            </ng-template>
            <ng-template #createdTmpl let-row="row" let-value="value">
                {{value | date :'longDate'}}
            </ng-template>
            <ng-template #updatedTmpl let-row="row" let-value="value">
                {{value | date :'longDate'}}
            </ng-template>
            <ng-template #hdrTpl let-column="column"> {{ column.name }}</ng-template>
            <ng-template #editTmpl let-row="row" let-value="value">
                <i class="fas fa-edit cursor-pointer mr-3 ml-3" data-toggle="tooltip" data-placement="top" title="Edit"
                    (click)="edit(value)" aria-hidden="true"></i>
                <!-- <i class="fa fa-trash cursor-pointer ml-2" data-toggle="tooltip" data-placement="top" title="Delete"
                    (click)="confirm(value)"></i> -->
            </ng-template>
        </div>
    </div>
</div>