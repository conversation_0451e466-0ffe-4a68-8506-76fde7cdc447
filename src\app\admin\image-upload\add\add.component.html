<!-- <div class="col-md-12">
    <form name="f" [formGroup]="ImageForm" class="form">
        <div class="col-md-6 form-group">
            <label for="position"> <sup>*</sup>Position</label>
            <input type="number" class="form-control" id="position" formControlName="posi"
                placeholder="Enter position" [ngClass]="{ 'is-invalid': submitted && f.posi.errors }">
            <span class="icon-position far fa-flag"
                [ngClass]="{ 'icon-error ': submitted && f.posi.errors }"></span>
            <div *ngIf="submitted && f.posi.errors" class="invalid-feedback">
                <p *ngIf="f.posi.errors.required">position is required</p>
            </div>
        </div>
        <div class="col-md-2 text-left">
            <label for="active" class="align-bottom pr-2">Active</label>
            <span>
                <ui-switch checked formControlName="acti" id="active" size="small" color='rgb(42,41,92)'>
                </ui-switch>
            </span>
        </div>
    </form>
    <div class=" mt-4 ">
        <button (click)="save()" class="next square" name="button">Submit</button>
    </div>
</div> -->

<nav aria-label="breadcrumb">
    <ol class="breadcrumb bg-transparent py-0 px-3">
        <li class="breadcrumb-item">
            <a class="cursor-pointer" [routerLink]="['/admin/imageupload']" [queryParams]="breadCumsData">Image</a>
        </li>
        <li class="breadcrumb-item active">{{edited ? "Edit": "Add"}}</li>
    </ol>
</nav>
<div class="container mt-4">
    <form [formGroup]="ImageForm" class="form form-horizontal" (ngSubmit)="create()">
        <div class="form-group ">
            <div class="col-md-12 form-group">
                    <label for="Position" style="display: block !important"> <sup>*</sup>Select Image</label>
                    <input type="file"  (change)="fileUpload($event)">
                <!-- ************************ -->
            </div>
            <div class="col-md-12 form-group row">
                <div class="col-md-6">
                    <label for="Position " > <sup>*</sup>Image Position</label>
                <input type="number" class="form-control" id="Position" formControlName="position"
                    placeholder="Enter Image Position" [ngClass]="{ 'is-invalid': submitted && f.Position.errors }">
                <span class="icon-position "
                    [ngClass]="{ 'icon-error ': submitted && f.Position.errors }"></span>
                <div *ngIf="submitted && f.position.errors" class="invalid-feedback">
                    <p *ngIf="f.position.errors.required"> Image Position is required</p>
                    <p *ngIf="f.position.errors.pattern"> Image Position is required</p>
                </div>
                </div>
                <div class="col-md-6">
                    <label for="active"> <sup>*</sup>active</label>
                    <input type="text" class="form-control" id="active" formControlName="active"
                        placeholder="Enter active" [ngClass]="{ 'is-invalid': submitted && f.active.errors }">
                    <span class="icon-active "
                        [ngClass]="{ 'icon-error ': submitted && f.active.errors }"></span>
                    <div *ngIf="submitted && f.active.errors" class="invalid-feedback">
                        <p *ngIf="f.active.errors.required"> active is required</p>
                        <p *ngIf="f.active.errors.pattern"> active is required</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-right pt-4">
                <button type="reset" (click)="listRecord()" class="btn btn-secondary mr-2">Cancel</button>
                <button type="submit" class="btn btn-primary text-right">{{edited ? "Update": "Add"}}</button>
            </div>
        </div>
    </form>
</div>