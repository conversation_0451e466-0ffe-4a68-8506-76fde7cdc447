import { AddComponent } from './../add/add.component';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { NgbModal, NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { CategoryService } from 'src/app/shared/services/category.service';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { NgxSpinnerService } from 'ngx-spinner';
import { Title } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'app-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss']
})
export class ListComponent implements OnInit, OnDestroy {

  addModal: NgbActiveModal;
  categories = [];
  categoriesLevelZero = [];
  categoriesLevelOne = [];
  categoriesLevelTwo = [];
  levelOne: any;
  levelTwo: any;
  parentData: any;
  languageResolve = [];
  loading: boolean = false;
  subscription: Subscription = new Subscription();

  constructor(private modalService: NgbModal, private categoryService: CategoryService,
              private route: ActivatedRoute, private spinner: NgxSpinnerService,
              private ts: Title, private translate: TranslateService) {
                translate.use('en');
                this.ts.setTitle('SEA Admin - Topics');
  }

  ngOnInit() {
    this.spinner.show(); 
    this.languageResolve = this.route.snapshot.data.language;
    this.categoryList();
  }
  add(event?, type?: number, count?: number, parentId?: any) {
    this.spinner.show(undefined, {
      type: 'ball-fussion',
      size: 'small',
      bdColor: 'rgb(255, 255, 255)',
      color: 'rgb(42 41 92)',
      fullScreen: false
    });
    const addModal = this.modalService.open(AddComponent, { size: 'xl'});
    addModal.componentInstance.selectedLevel = type;
    addModal.componentInstance.selectedCategory = event;
    addModal.componentInstance.parentId = parentId;
    addModal.componentInstance.languages = this.languageResolve || [];
    addModal.componentInstance.totalCount = count;
    addModal.result.then(data => {
      this.spinner.hide();
      this.loading = false; // Update loading state

      if (event) {
        this.updateTopics(data, type, event);
      } else {
        this.categories.push(data);
        // tslint:disable-next-line:triple-equals
        if (parentId.parentId == data.parentId.id) {
          switch (type) {
            case 0: this.categoriesLevelZero.push(data); break;
            case 1: this.categoriesLevelOne.push(data); break;
            case 2: this.categoriesLevelTwo.push(data); break;
          }
        }
      }
    }, (data) => {
      // this.spinner.hide();
      // this.loading = false;
    });
  }
 
  categoryList() {
    this.spinner.show(); // Start spinner
    this.loading = true; 

    const categories = this.categoryService.getCategory().subscribe(
      (res: any) => {    

        this.categories = res;
        this.setCategories();

        this.spinner.hide(); // Stop spinner on success
        this.loading = false; // Update loading state
      },
      error => {
        this.spinner.hide(); // Stop spinner on error
        this.loading = false; // Update loading state
        throw error;
      }
    );
    this.subscription.add(categories);
  }

  setCategories() {
    this.categoriesLevelZero = this.categories.filter(data => {
      return data.level === 0;
    });
    this.categoriesLevelOne = this.categories.filter(data => {
      return data.level === 1;
    });

    this.categoriesLevelTwo = this.categories.filter(data => {
      return data.level === 2;
    });
  }
  levelData(data, type?) {
    switch (type) {
      case 'zero': this.levelOne = data.id; this.levelTwo = null;
                   this.categoriesLevelOne = this.categories.filter(item => {
          const parentId = (item.parentId !== null && typeof item.parentId === 'object') ? item.parentId.id : item.parentId;
          if (parentId) {
            return this.levelOne === parentId;
          }
        });
                   break;
      case 'one': this.levelTwo = data.id;
                  this.categoriesLevelTwo = this.categories.filter(item => {
          const parentId = (item.parentId !== null && typeof item.parentId === 'object') ? item.parentId.id : item.parentId;
          if (parentId) {
            return this.levelTwo === parentId;
          }
        });
                  break;
    }
  }
  updateLevelslist(data, type) {
    switch (type) {
      case 'zero': this.levelOne = null; this.levelTwo = null; break;
      case 'one': this.levelTwo = null; break;
    }
  }
  updateTopics(event, type, selectedData) {
    const data = event;
    let categories;
    const temp = this.categories.findIndex(item => item.id === data.id);
    this.categories[temp] = data;
    switch (type) {
      case 0: categories = this.categoriesLevelZero;
              break;
      case 1: categories = this.categoriesLevelOne;
              break;
      case 2: categories = this.categoriesLevelTwo;
              break;
    }
    const index = categories.findIndex(item => item.id === data.id);
    // tslint:disable-next-line:triple-equals
    if (type == 0 || data.parentId.id == selectedData.parentId ||  data.parentId.id == selectedData.parentId.id) {
      categories[index] = data;
    } else {
      categories.splice(index, 1);
    }
  }
  updateDeletedlevels(event, type) {
    const parentId = (event.parentId !== null && typeof event.parentId === 'object') ? event.parentId.id : event.parentId;
    const levelId = event.id;
    const element = this.categories.findIndex(item => item.id === levelId);
    this.categories.splice(element, 1);
    this.categories = [...this.categories];
    switch (type) {
      case 'zero': const index = this.categoriesLevelZero.findIndex(item => item.id === levelId);
                   this.categoriesLevelZero.splice(index, 1);
                   this.categoriesLevelZero = [...this.categoriesLevelZero];
                   break;

      case 'one': const temp = this.categoriesLevelOne.findIndex(item => item.id === levelId);
                  this.categoriesLevelOne.splice(temp, 1);
                  this.categoriesLevelOne = [...this.categoriesLevelOne];
                  if (this.categoriesLevelOne.length === 0) {
          const leafData = this.categoriesLevelZero.find(item => {
            if (item.id === parentId) {
              item.isLeaf = true;
              return item;
            }
          });
        }
                  break;

      case 'two': const data = this.categoriesLevelTwo.findIndex(item => item.id === levelId);
                  this.categoriesLevelTwo.splice(data, 1);
                  this.categoriesLevelTwo = [...this.categoriesLevelTwo];
                  if (this.categoriesLevelTwo.length === 0) {
          const leafData = this.categoriesLevelOne.find(item => {
            if (item.id === parentId) {
              item.isLeaf = true;
              return item;
            }
          });
        }
                  break;
    }
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
