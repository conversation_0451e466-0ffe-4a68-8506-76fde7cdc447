import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { UntypedFormBuilder, Validators, UntypedFormGroup, UntypedFormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { NgxSpinnerService } from 'ngx-spinner';
import { Observable, Subject, merge, Subscription, combineLatest } from 'rxjs';
import { ExpertService } from 'src/app/shared/services/expert.service';
import { Expert } from 'src/app/shared/models/expert.model';
import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { CategoryService } from 'src/app/shared/services/category.service';
import { FileUploadService } from 'src/app/shared/services/file-upload.service';
import { debounceTime, distinctUntilChanged, map, filter } from 'rxjs/operators';
import { DomSanitizer } from '@angular/platform-browser';
import { Ng2ImgMaxService } from 'ng2-img-max';
import { SystemFieldsService } from 'src/app/shared/services/system-fields.service';


@Component({
  selector: 'app-upsert',
  templateUrl: './upsert.component.html',
  styleUrls: ['./upsert.component.scss']
})
export class UpsertComponent implements OnInit, OnDestroy {
  expertForm: UntypedFormGroup;
  showTo; ast = true;
  id: number;
  topicsList: any;
  toggleName: boolean;
  selectedFiles: any;
  filesData = [];
  pageOffset: any;
  submitted = false;
  reqLabel = false;
  languageResolve = [];
  expertsType = [];
  profilePreview: any;
  noPreview = 'assets/logo/no-image.png';
  fileName: string;
  language: UntypedFormArray;
  subscription: Subscription = new Subscription();
  @ViewChild('instance', { static: true }) instance: NgbTypeahead;
  @ViewChild('instance', { static: true }) instanceTopics: NgbTypeahead;
  @ViewChild('topicElement', { static: true }) topicElement: ElementRef;
  focusCountry$ = new Subject<string>();
  selectCountry$ = new Subject<string>();
  focusTopics$ = new Subject<string>();
  selectTopics$ = new Subject<string>();
  uploadedImage: Blob;


  countries = [
    'Afghanistan',
    'Albania',
    'Algeria',
    'Antigua and Barbuda',
    'Argentina',
    'Armenia ',
    'Australia',
    'Austria',
    'Azerbaijan',
    'Azores',
    'Bahamas',
    'Bangladesh',
    'Barbados',
    'Belarus',
    'Belgium',
    'Belize',
    'Bermuda',
    'Bolivia',
    'Bosnia & Herzegovina',
    'Brazil',
    'Bulgaria',
    'Cambodia',
    'Cameroon',
    'Canada',
    'Cape Verde',
    'Chile',
    'China',
    'Columbia',
    'Costa Rica',
    'Croatia',
    'Cuba',
    'Cyprus',
    'Czech Republic',
    'Czechoslovakia',
    'Denmark',
    'Dominica',
    'Dominican Republic',
    'Ecuador',
    'Egypt',
    'El Salvador',
    'England',
    'Eritrea',
    'Ethiopia',
    'Fiji',
    'Finland',
    'France',
    'Georgia',
    'Germany',
    'Ghana',
    'Greece',
    'Grenada',
    'Guam',
    'Guatemala',
    'Guyana',
    'Haiti',
    'Honduras',
    'Hong Kong',
    'Hungary',
    'India',
    'Indonesia',
    'Iran',
    'Iraq',
    'Ireland',
    'Israel',
    'Italy',
    'Jamaica',
    'Japan',
    'Jordan',
    'Kenya',
    'Korea',
    'Kosovo',
    'Kuwait',
    'Laos',
    'Latvia',
    'Lebanon',
    'Liberia',
    'Lithuania',
    'Luxembourg',
    'Macedonia',
    'Malaysia',
    'Mexico',
    'Moldova',
    'Morocco',
    'Myanmar(Burma)',
    'Nepal',
    'Netherlands',
    'New Zealand',
    'Nicaragua',
    'Nigeria',
    'Northern Ireland',
    'Norway',
    'Other U.S.Island Areas',
    'Oman',
    'Pakistan',
    'Panama',
    'Paraguay',
    'Peru',
    'Philippines',
    'Poland',
    'Portugal',
    'Qatar',
    'Puerto Rico',
    'Romania',
    'Russia',
    'Samoa',
    'Saudi Arabia',
    'Scotland',
    'Senegal',
    'Serbia',
    'Sierra Leon ',
    'Singapore',
    'Slovakia',
    'Somalia',
    'South Africa',
    'South Korea',
    'Spain',
    'Sri Lanka',
    'St.Kitts--Nevis',
    'St.Lucia',
    'St.Vincent and the Grenadines',
    'Sudan',
    'Sweden',
    'Switzerland',
    'Syria',
    'Taiwan',
    'Tanzania',
    'Thailand',
    'Tonga',
    'Trinidad and Tobago',
    'Turkey',
    'U.S.Virgin Islands',
    'UAE',
    'Uganda',
    'Ukraine',
    'United Kingdom',
    'United States',
    'Uruguay',
    'USSR',
    'Uzbekistan',
    'Venezuela',
    'Vietnam',
    'Wales',
    'Yemen',
    'Yugoslavia',
    'Zimbabwe'
  ];
  selectedImages = [];
  nonWhitespaceRegExp = new RegExp('\\S');
  urlTRegExp = new RegExp('(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})');
  breadCumsData: any;
  isSubAdmin = false;
  emailRegex = new RegExp('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$');

  constructor(
    private fb: UntypedFormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private spinner: NgxSpinnerService,
    private toaster: ToastrService,
    private expertService: ExpertService,
    private sanitizer: DomSanitizer,
    private ng2ImgMax: Ng2ImgMaxService,
    private fileUploadService: FileUploadService,
    private topicService: CategoryService,
    private systemFields: SystemFieldsService,
  ) { 
    this.getSegementData();
  }

  ngOnInit() {
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.isSubAdmin = true;
    }
    this.getTopics();
    this.createForm();
    this.pageOffset = this.route.snapshot.queryParams.offset;
    this.profilePreview = 'assets/logo/avatar.png';
    if (this.route.snapshot.params.id) {
      this.toggleName = true;
      this.id = this.route.snapshot.params.id;
      this.patchForm();
    }
    this.breadCumsData = {
      limit: 10,
      offset: this.pageOffset,
      search: '',
      sortName: 'createdAt',
      sortValue: 'desc'
    };
  }
  getSegementData() {
    this.systemFields.getSystemFields('Experts Type').subscribe((res: any) => {
      if (res.length) {
        this.expertsType = res.map(val => {
          return val.fieldName;
        });
      } else {
        this.expertsType = ['Topic', 'Support'];
      }
    }, (error) => {
      this.spinner.hide();
    })
  }
  createForm() {
    this.expertForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]],
      lastName: ['', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]],
      email: ['', [Validators.required, Validators.pattern(this.emailRegex)]],
      contactNumber: '',
      topic: [''],
      country: ['', [Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]],
      type: ['', [Validators.required]],
      bestPractices: '',
      designation: ['', [Validators.pattern(this.nonWhitespaceRegExp)]],
      imageUrl: '',
      expertUrl: ['', [Validators.required, Validators.pattern(this.urlTRegExp)]],
    });
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.expertForm.patchValue({
        country: sessionStorage.getItem('country'),
      });
    }
  }

  getTopics() {
    const topicList$ = this.topicService.getCategory().subscribe((res: any) => {
      this.topicsList = res.filter((items => {
        return items.level === 0;
      }));
    }, (error) => {
      throw error;
    });
    this.subscription.add(topicList$);
  }

  get f() {
    return this.expertForm.controls;
  }

  // Edit and Populating Segments
  patchForm() {
    this.spinner.show();
    const export$ = this.expertService.getRecordById(this.id).subscribe((res: Expert) => {
      this.checkExpertType(res.type);
      this.expertForm.patchValue({
        firstName: res.firstName,
        lastName: res.lastName,
        email: res.email ? res.email : '',
        contactNumber: res.contactNumber,
        country: res.country,
        type: res.type,
        topic: res.topic,
        bestPractices: res.bestPractices ? this.fileName = res.bestPractices : '',
        designation: res.designation ? res.designation : '',
        imageUrl: res.imageUrl ? this.profilePreview = res.imageUrl : this.profilePreview,
        expertUrl : res.expertUrl
      });
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      this.listRecord();
      throw error;
    });
    this.subscription.add(export$);
  }

  onTypeChange(val) {
    this.checkExpertType(val);
  }

  checkExpertType(position) {
    if (position === 'Topic') {
      this.reqLabel = true;
      this.expertForm.get('topic').setValidators([Validators.required, Validators.pattern(this.nonWhitespaceRegExp)]);
      this.expertForm.get('topic').updateValueAndValidity();
      this.topicElement.nativeElement.disabled = false;
    } else {
      this.expertForm.get('topic').clearValidators();
      this.expertForm.get('topic').updateValueAndValidity();
      this.topicElement.nativeElement.value = '';
      this.topicElement.nativeElement.disabled = true;
      this.topicElement.nativeElement.classList.remove('is-invalid');
      this.reqLabel = false;
    }
  }

  fileChange(event) {
    if (event.target.files.length > 0) {
      this.selectedFiles = event.target.files[0];
      const ext = this.selectedFiles.name.split('.').pop();
      if (ext === 'pdf' || ext === 'docx' || ext === 'doc' || ext === 'xls' || ext === 'xlsx') {
        this.filesData[0] = { name: 'document', value: this.selectedFiles };
        this.fileName = this.selectedFiles.name;
      } else {
        // const images$ = this.ng2ImgMax.resizeImage(this.selectedFiles, 400, 300).subscribe(
        //   result => {
        //     this.uploadedImage = result;
        //   },
        //   error => {

        //   }
        // );
        // this.subscription.add(images$);
        this.filesData[1] = { name: 'image', value: this.selectedFiles };
        const reader = new FileReader();
        reader.readAsDataURL(this.selectedFiles);
        reader.onload = (res: any) => {
          this.profilePreview = res.target.result;
          this.profilePreview = this.sanitizer.bypassSecurityTrustUrl(this.profilePreview);
        };
      }
    }
  }

  uploadFile(data, payload) {
    data.forEach(items => {
      const formData = new FormData();
      formData.append('file', items.value);
      const photoObject = this.fileUploadService.uploadFile(formData).pipe(map((eve: any) => {
        return { name: items.name, url: eve.url };
      }));
      this.selectedImages.push(photoObject);
    });

    const photos$ = combineLatest(this.selectedImages).subscribe((val: any) => {
      if (val.length > 0) {
        const resDoc: any = val.find((item: any) => item.name === 'document');
        const resImg: any = val.find((item: any) => item.name === 'image');
        payload.bestPractices = resDoc ? resDoc.url : payload.bestPractices;
        payload.imageUrl = resImg ? resImg.url : payload.imageUrl;
      }
      if (this.id) {
        this.updateExperts(payload);
      } else {
        this.addExperts(payload);
      }
    });
    this.subscription.add(photos$);
  }

  // Adding a Expert
  submit() {
    this.submitted = true;
    if (this.expertForm.value.email) {
      this.f.expertUrl.clearValidators();
      this.f.expertUrl.updateValueAndValidity();
    } else if (this.expertForm.value.expertUrl) {
      this.f.email.clearValidators();
      this.f.expertUrl.clearValidators();
      this.f.expertUrl.updateValueAndValidity();
      this.f.email.updateValueAndValidity();
    }
    if (this.expertForm.invalid) {
      return;
    } else {
      const payload = this.expertForm.getRawValue();
      payload.imageUrl = payload.imageUrl === 'assets/logo/avatar.png' ? payload.imageUrl = '' : payload.imageUrl;
      payload.topic = payload.type === 'Topic' ? payload.topic = this.expertForm.controls.topic.value.id : payload.topic = null;
      payload.email = !!payload.email ? payload.email : null;
      payload.expertUrl = !!payload.expertUrl ? payload.expertUrl : null;
      this.spinner.show();
      if (this.id) {
        if (this.filesData.length) {
          this.uploadFile(this.filesData, payload);
        } else {
          this.updateExperts(payload);
        }
      } else {
        if (this.filesData.length) {
          this.uploadFile(this.filesData, payload);
        } else {
          this.addExperts(payload);
        }
      }

    }
  }

  // Country Look Up
  resultLookUp(value: any) {
    return value;
  }

  inputLookUp(value: any) {
    if (value) {
      return value;
    }
    return value;
  }

  searchExpertsCountry = (text$: Observable<string>) => {
    const deBouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.selectCountry$.pipe(filter(() => !this.instance.isPopupOpen()));
    const inputFocus$ = this.focusCountry$;
    return merge(deBouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.countries.filter(item =>
          // tslint:disable-next-line: max-line-length
          item.toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  // Service LookUp
  resultTopics(value: any) {
    return value.name;
  }

  inputTopics(value: any) {
    if (value.id) {
      return value.name;
    }
    return value;
  }

  searchTopics = (text$: Observable<string>) => {
    const deBouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.selectTopics$.pipe(filter(() => !this.instanceTopics.isPopupOpen()));
    const inputFocus$ = this.focusTopics$;
    return merge(deBouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => term.length < 0 ? []
        : this.topicsList.filter(item =>
          // tslint:disable-next-line: max-line-length
          item.name.toLowerCase().indexOf(term.toLowerCase()) > -1))
    );
  }

  // Update Service
  updateExperts(payload) {
    const export$ = this.expertService.updateRecord(this.id, payload).subscribe((res: Expert) => {
      this.toaster.success('Expert updated successfully');
      this.listRecord();
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      if (error.error.data.message === 'Would violate uniqueness constraint-- a record already exists with conflicting value(s).') {
        this.toaster.error('Expert already exists for this Topic');
        // throw error;
      } else {
        throw error;
      }
    });
    this.subscription.add(export$);
  }

  addExperts(payload) {
    const export$ = this.expertService.createRecord(payload).subscribe((res: Expert) => {
      this.toaster.success('Expert created successfully');
      this.listRecord();
      this.spinner.hide();
    }, error => {
      this.spinner.hide();
      if (error.error.data.message === 'Would violate uniqueness constraint-- a record already exists with conflicting value(s).') {
        this.toaster.error('Expert Already Exists for this Topic');
        // throw error;
      } else {
        throw error;
      }
    });
    this.subscription.add(export$);
  }
  checkValidator(event, type) {
    if (type === 'expertUtl' && !event.length) {
      this.f.expertUrl.setValidators([Validators.required, Validators.pattern(this.urlTRegExp)]);
      this.f.expertUrl.updateValueAndValidity();
    } else if (type === 'email' && !event.length) {
      this.f.email.setValidators([Validators.required, Validators.pattern(this.emailRegex)]);
      this.f.email.updateValueAndValidity();
    } else {
      this.f[type].clearValidators();
      this.f[type].updateValueAndValidity();
    }
  }
  close() {
    this.listRecord();
  }

  listRecord() {
    this.router.navigate(['admin/experts'], {
      queryParams: this.breadCumsData
    });
  }

  trackByFn(index) {
    return index;
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}

