import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { BestPracticeRoutingModule } from './best-practice-routing.module';
import { ListComponent } from './list/list.component';
import { UpsertComponent } from './upsert/upsert.component';
import { SelectDropDownModule } from 'ngx-select-dropdown';
import { MaterialModule } from 'src/app/shared/material.module';


@NgModule({
  declarations: [ListComponent, UpsertComponent],
  imports: [
    CommonModule,
    BestPracticeRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    NgbModule,
    NgxDatatableModule,
    SelectDropDownModule,
    MaterialModule,
  ]
})
export class BestPracticeModule { }
