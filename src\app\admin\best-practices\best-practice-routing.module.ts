import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListComponent } from './list/list.component';
import { UpsertComponent } from './upsert/upsert.component';
import { LanguageResolve } from 'src/app/shared/resolves/language.resolve';


const routes: Routes = [
  {
    path: '',
    component: ListComponent
  },
  {
    path: 'add',
    component: UpsertComponent,
    resolve: { language: LanguageResolve }
  },
  {
    path: 'edit/:id',
    component: UpsertComponent,
    resolve: { language: LanguageResolve }
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BestPracticeRoutingModule { }
