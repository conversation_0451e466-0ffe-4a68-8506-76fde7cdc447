import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ColumnMode, DatatableComponent } from '@swimlane/ngx-datatable';
import { BestPracticeService } from 'src/app/shared/services/best-practices.service';
import { AddComponent } from '../add/add.component';
import { ToastrService } from 'ngx-toastr';
import { NgxSpinnerService } from 'ngx-spinner';
import { ConfirmationWindowComponent } from 'src/app/shared/components/confirmation-window/confirmation-window.component';
import { Subject, Subscription } from 'rxjs';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { ConstantData } from 'src/app/shared/constants/constant';
@Component({
  selector: 'app-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss']
})
export class ListComponent implements OnInit {
  @ViewChild('Initiative') Initiative: TemplateRef<any>;
  // @ViewChild('checkboxTemplate', { static: true }) checkboxTemplate: TemplateRef<any>;
    @ViewChild('createdTmpl', { static: true }) createdTmpl: TemplateRef<any>;
    @ViewChild('updatedTmpl', { static: true }) updatedTmpl: TemplateRef<any>;
    @ViewChild('editTmpl', { static: true }) editTmpl: TemplateRef<any>;
    @ViewChild('hdrTpl', { static: true }) hdrTpl: TemplateRef<any>;
    @ViewChild('dataTable', { static: true }) table: DatatableComponent;
    
  initiativeData: any = [];
  limit = 10;
  offset = 0;
  count = 0;
  readonly headerHeight = 50;
  readonly footerHeight = 50;
  readonly rowHeight = 50;
  readonly pageLimit = 25;
  offsetPaginate = 0;
  ColumnMode = ColumnMode;
  dialogInitiative: any;
  searchText = '';
  updateIDs = [];
  InActivateData = [];
  activateData = [];
  statusActive: boolean;
  statusInActive: boolean;
  subscription: Subscription = new Subscription();
  isSubAdmin = false;
  totalCount: number;
  practiceList: any;
  initiatives: any[] = [];
  sortBy: any = { name: 'name', value: 'ASC' };
  columns: any[] = [];
  

  @ViewChild('listTemplate') listTemplate;
  @ViewChild('rowTemplate') rowTemplate;
  @ViewChild('checkboxTemplate') checkboxTemplate;

  constructor(
    private practiceService: BestPracticeService,
    private dialog: MatDialog,
    private toaster: ToastrService,
    private spinner: NgxSpinnerService,
    private modalService: NgbModal,

    
  ) { }
  ;
  ngOnInit() {
    this.columns = [
      {
        cellTemplate: this.checkboxTemplate,
        name: '',
        prop: 'id',
        sortable: false,
        minWidth: 75,
        maxWidth: 100
      },
      {
        name: 'Initiative Id',
        prop: 'initiativeid',
        cellTemplate: this.listTemplate,
        minWidth: 100
      },
      {
        name: 'Initiative Name',
        prop: 'initiativename',
        cellTemplate: this.listTemplate,
        minWidth: 180
      },
      {
        name: 'Country',
        prop: 'country',
        cellTemplate: this.listTemplate,
        minWidth: 100
      },
      {
        name: 'Address',
        prop: 'address',
        cellTemplate: this.listTemplate,
        minWidth: 100
      },
      {
        name: 'Contact',
        prop: 'contact',
        cellTemplate: this.listTemplate,
        minWidth: 180
      },
      {
        name: 'Investment',
        prop: 'investment',
        cellTemplate: this.listTemplate,
        minWidth: 75
      },
      {
        name: 'Saving',
        prop: 'saving',
        cellTemplate: this.listTemplate,
        minWidth: 180
      },
      {
        name: 'Period',
        prop: 'period',
        cellTemplate: this.listTemplate,
        minWidth: 100
      },
      {
        name: 'Score',
        prop: 'score',
        cellTemplate: this.listTemplate,
        minWidth: 50
      },
      {
        name: 'Action',
        prop: 'Action',
        cellTemplate: this.rowTemplate,
        minWidth: 50,
        maxWidth: 500
      },
      {
        name: 'IsDeleted',
        prop: 'status',
        cellTemplate: this.checkboxTemplate,
        minWidth: 75
      }
    ];
    if (sessionStorage.getItem('role') === 'SubAdmin') {
      this.isSubAdmin = true;
    }
    this.getInitiative();
    
  }
  getInitiative() {
    this.spinner.show();
    this.practiceService.getIntiativeData().subscribe((res: any) => {  
      this.spinner.hide();
    this.initiativeData = res.map((item: any) => ({
      ...item,
      country: item.country && item.country.length > 0 ? item.country.map((data: any) => data.name).join(', ') : '-'
    }));
      this.count = res.length
    },  (err: any) => {
      this.spinner.hide();
    })
  }

  onInputChange(e) {
    if (!!this.searchText) {
      this.practiceService.searchIntiativeData(this.searchText).subscribe((res: any) => {
        this.initiativeData = res.data;
      });
    }else {
      this.getInitiative();
    }
  }

  openInitiative(editInitiative, id) {
    const dialogRef = this.dialog.open(AddComponent, {
      disableClose: true,
      hasBackdrop: true,
      panelClass: 'logFile',
      maxHeight: '100%',
    });
    dialogRef.componentInstance.editInitiative = editInitiative;
    dialogRef.componentInstance.id = id;
    dialogRef.afterClosed().subscribe(result => {
      this.getInitiative();
    });
  }

  deleteData(row) {
    this.spinner.show();
    this.practiceService.deleteIntiativeData(row.id).subscribe(res => {
      this.toaster.success('Deleted Initiative Successfully');
      this.getInitiative()
      this.spinner.hide();
    }, error => {
      this.toaster.warning('Something Went Wrong');
      this.spinner.hide();
    });

  }

  confirm(props) {
    this.statusActive = this.activateData.some(item => this.updateIDs.includes(item));
    this.statusInActive = this.InActivateData.some(item => this.updateIDs.includes(item));
    if (props === 'activate' && !this.statusActive) {
      this.callModal(props);
    } else if (props === 'deactivate' && !this.statusInActive) {
      this.callModal(props);
    } else if (props === 'deactivate' && (this.statusInActive && this.statusActive)) {
      this.callModal(props);
    } else if (props === 'activate' && (this.statusInActive && this.statusActive)) {
      this.callModal(props);
    } else {
      // this.changeDetectorRef.detectChanges();
      this.toaster.warning(`${this.updateIDs.length > 1 ? 'Experts' : 'Expert'} Already ${props}d `);
      const checkBox = document.getElementsByName('check');
      checkBox.forEach((item: HTMLInputElement) => item.checked = false);
      this.updateIDs = [];
      // const deleteModal = this.modalService.open(ConfirmationWindowComponent);
      // deleteModal.componentInstance.title = ConstantData.deleteInfo.title;
      // deleteModal.componentInstance.content = ConstantData.deleteInfo.content;
      // deleteModal.componentInstance.recordId = props;
      // deleteModal.result.then(() => {
      //   this.remove(props);
      // }, (data) => { });
    }
  }

  callModal(props) {
    if (props === 'activate') {
      const activateModal = this.modalService.open(ConfirmationWindowComponent);
      activateModal.componentInstance.title = ConstantData.ActivateInfo.title;
      activateModal.componentInstance.content = ConstantData.ActivateInfo.content;
      activateModal.componentInstance.updateState = props;
      activateModal.result.then((res: any) => {
        this.updateStatus(res);
      }, (data) => { });

    } else if (props === 'deactivate') {
      const deactivateModal = this.modalService.open(ConfirmationWindowComponent);
      deactivateModal.componentInstance.title = ConstantData.DeactivateInfo.title;
      deactivateModal.componentInstance.content = ConstantData.DeactivateInfo.content;
      deactivateModal.componentInstance.updateState = props;
      deactivateModal.result.then((res: any) => {
        this.updateStatus(res);
      }, (data) => { });

    } else {
      return false;
    }
  }

  // Bulk Update Selection Logic
  updateStatus(updateStatus) {
    let selectedId;
    if (updateStatus === 'activate') {
      selectedId = this.updateIDs.filter(i => !this.initiatives.filter(item => item.isActive === 'Active').map(itm => itm.id).includes(i));
    } else if (updateStatus === 'deactivate') {
      selectedId = this.updateIDs.filter(i => !this.initiatives.filter(item => item.isActive === 'Inactive').map(itm => itm.id).includes(i));
    }
    if (selectedId.length) {
      const payload = selectedId.map((item) => {
        return {
          id: item,
          isActive: updateStatus === 'activate' ? true : false,
        };
      });
      const segmentStatus$ = this.practiceService.updateInitiativeStatus(payload).subscribe((res) => {
        this.toaster.success(`${selectedId.length > 1 ? 'Initiatives' : 'Initiative'} ${updateStatus === 'deactivate' ? 'Deactivated' : 'Activated'}  Successfully`);
        const checkBox = document.getElementsByName('check');
        checkBox.forEach((item: HTMLInputElement) => item.checked = false);
        this.getInitiative();
        // this.updateIDs = [];
        // this.activateData = [];
        // this.InActivateData = [];
        // this.getDataSource();
      }, error => {
        throw error;
      });
      this.subscription.add(segmentStatus$);
    }
  }

  // Common Service
    getDataSource(offset?) {
      this.spinner.show();
      let search;
      const countryCondition = this.isSubAdmin ? sessionStorage.getItem('country') : '';
      if (this.isSubAdmin) {
        this.searchText.length ?
          search = `&where=` + JSON.stringify({ name: { contains: this.searchText }, country: countryCondition }) : search = '';
      } else {
        this.searchText.length ? search = `&where=` + JSON.stringify({ name: { contains: this.searchText } }) : search = '';
      }
      const segments$ = this.practiceService.getRecord(this.limit, offset != undefined ? offset : this.offset, this.sortBy, search, countryCondition ? `country=${countryCondition}` : '')
        .subscribe((res: any) => {
          this.spinner.hide();
          this.totalCount = parseInt(res.headers.get('content-count'), 10);
          this.initiatives = res.body;
          this.spinner.show();
          this.practiceList = this.initiatives;
          this.spinner.hide();
  
          // Adding Nil(-) part
          this.initiatives.map((item: any) => {
            if (item.topic == null) {
              item.topicName = '-';
              return item;
            } else {
              item.topicName = item.topic.name;
              return item;
            }
          });
  
          this.initiatives.map((item: any) => {
            if (item.isActive) {
              item.isActive = 'Active';
              this.activateData.push(item.id);
            } else {
              item.isActive = 'Inactive';
              this.InActivateData.push(item.id);
            }
          });
          this.initiatives = this.initiatives.map((item, i) => {
            item.index = i + 1 + this.offset;
            return item;
          });
        }, error => {
          this.spinner.hide();
          throw error;
        });
      this.subscription.add(segments$);
    }

    // Bulk Update Selection
  CheckRecords(template, val) {
    if (template.checked) {
      this.updateIDs.push(val.id);
      
    } else {
      const index = this.updateIDs.findIndex(item => item === val.id);
      this.updateIDs.splice(index, 1);
    }
  }
}
