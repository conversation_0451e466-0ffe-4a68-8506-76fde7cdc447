<div class="container">
    <form [formGroup]="form">
        <div class="m-0 modal-header ">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div class="d-flex w-100 justify-content-between">
                    <div class="w-75 d-flex justify-content-between">
                        <h2 class="m-0 p-0 text-dark w-100">Topic</h2>
                    </div>
                    <div style="align-items: center;display: flex;" class="w-25">
                        <button ngbTooltip="close" placement="left" type="button" class="close" aria-label="Close"
                            (click)="close()">
                            <span>
                                <i class="fas fa-times text-muted fa-xs"></i>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-body h-100 mdc-dialog__content mt-2" style="min-height: 55vh;max-height: 55vh;">
            <div class="form-group row">
                <div class="col-md-12">
                    <ul class="nav nav-pills nav-fill">
                        <li class="nav-item cursor-pointer">
                            <a class="nav-link" [class.active-tab]="topicDetails"
                                (click)="topicDetails = !topicDetails">{{selectedCategory
                                ? 'Edit' : 'Create'}}</a>
                        </li>
                        <li class="nav-item cursor-pointer">
                            <a class="nav-link" [class.active-tab]="!topicDetails"
                                (click)="topicDetails = !topicDetails">Multi-Language</a>
                        </li>
                    </ul>
                </div>
            </div>
            <ng-container *ngIf="topicDetails">
                <div class="form-group row pt-3 px-3 scrollable-content h-100">
                    <div class="col-md-6 form-group">
                        <label for="topicName"> <sup>*</sup>Topic Name</label>
                        <input type="text" class="form-control" placeholder="Enter Topic Name" id="topicName"
                            formControlName="categoryName" [ngClass]="{ 'is-invalid': submitted && g.categoryName.errors}">
                        <div class="col-md-12 invalid-feedback" *ngIf="submitted && g.categoryName.errors">
                            <p *ngIf="submitted && g.categoryName.errors.required">
                                Topic Name is required
                            </p>
                            <p *ngIf="submitted && g.categoryName.errors?.pattern">
                                Topic Name is required
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6 form-group">
                        <label for="services"><sup>*</sup> Services</label>
                        <ngx-select-dropdown  formControlName="services" [multiple]="true" [config]="config"
                            [options]="services" [ngClass]="{ 'is-invalid': submitted && g.services.errors }"
                            *ngIf="!selectedCategory" class="">
                        </ngx-select-dropdown>
    
                        <ngx-select-dropdown  formControlName="services" [multiple]="true" [config]="config"
                            [options]="services" [ngClass]="{ 'is-invalid': submitted && g.services.errors }"
                            *ngIf="selectedCategory" class="">
                        </ngx-select-dropdown>
                        <!-- <select class="form-control" id="service" placeholder="Service" formControlName="services" multiple
                            *ngIf="selectedCategory">
                            <option *ngFor="let service of services;trackBy: trackByFn" [value]=service.id>{{service.name}}
                            </option>
                        </select> -->
                        <div class="col-md-12 invalid-feedback" *ngIf="submitted && g.services.errors">
                            <p *ngIf="submitted && g.services.errors.required">
                                Services is required
                            </p>
                        </div>
                    </div>
    
                </div>
                <div class="form-group row pt-3 px-3 h-100">
                    <div class="col-md-6 form-group" *ngIf="showUpload">
                        <label for="colorPicker"><sup>*</sup>Choose a color for topic</label>
                        <input class="form-control" id="colorPicker" [(colorPicker)]="colorValue"
                            [style.background]="colorValue" [cpPosition]="bottom" (colorPickerChange)="colorChanged()"
                            [ngClass]="{ 'is-invalid': submitted && g.color.errors }" />
                        <input type="hidden" name="color" formControlName="color">
                        <div class="col-md-12 invalid-feedback" *ngIf="submitted && g.color.errors">
                            <p *ngIf="submitted && g.color.errors.required">
                                Topic color is required
                            </p>
                        </div>
                    </div>
                    <div class="form-group col-md-6" *ngIf="!showUpload">
                        <label for="parent"> <sup>*</sup>Parent</label>
                        <select class="form-control" placeholder="Select parent level" id="parent" formControlName="parent"
                            (change)="getChangedParent()">
                            <option value="" disabled>Choose parent level</option>
                            <option *ngFor="let category of categories;trackBy: trackByFn" [value]=category.id>
                                {{category.name}}</option>
                        </select>
    
                    </div>
                    <div class="form-group col-md-6">
                        <label for="level"><sup>*</sup> Level</label>
                        <input class="form-control" placeholder="Enter a level" id="level" formControlName="level"
                            [ngClass]="{ 'is-invalid': submitted && g.level.errors}">
                        <div class="col-md-12 invalid-feedback" *ngIf="submitted && g.level.errors">
                            <p *ngIf="submitted && g.level.errors.required">
                                Level is required
                            </p>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label for="Discription"><sup>*</sup> Description</label>
                        <input class="form-control" placeholder="Enter a Description" id="Discription"
                            formControlName="description">
                    </div>
                    <div class="from-group col-md-12">
                        <label for="summary">Summary</label>
                        <ckeditor formControlName="summary" [editor]="Editor" [config]="csconfig"></ckeditor>
                    </div>
                </div>
                <div class="form-group row pt-3 px-3" *ngIf="showUpload">
    
                    <div class="card" class="col-md-6 text-center pt-4 px-6">
                        <div class="card-body text-center">
                            <img [src]="images[0].url" class="text-center" width="200">
                        </div>
                        <input id="topicImage" type="file" accept="image/*" (change)="fileChange($event, 'topic')"
                            accept="image/*"
                            [ngClass]="{ 'is-invalid': submitted && g.topicImage.errors,'border-danger':submitted && g.topicImage.errors }"
                            style="visibility:hidden;width:0px">
                        <input type="hidden" name="topicImage" formControlName="topicImage">
                        <label for="topicImage" class="btn btn-primary">Upload topic image</label>
                        <div *ngIf="submitted && g.topicImage.errors" class="invalid-feedback display-block">
                            <div *ngIf="g.topicImage.errors.required">Image is required</div>
                        </div>
                    </div>
                    <div class="card" class="col-md-6 text-center pt-4 px-6">
                        <div class="card-body text-center">
                            <img [src]="images[1].url" class="text-center" width="200">
                        </div>
                        <input id="badgeImage" type="file" accept="image/*" (change)="fileChange($event, 'badge')"
                            accept="image/*"
                            [ngClass]="{ 'is-invalid': submitted && g.badgeImage.errors,'border-danger':submitted && g.badgeImage.errors }"
                            style="visibility:hidden;width:0px">
                        <input type="hidden" name="badgeImage" formControlName="badgeImage">
                        <label for="badgeImage" class="btn btn-primary">Upload badge image</label>
                        <div *ngIf="submitted && g.badgeImage.errors" class="invalid-feedback display-block">
                            <div *ngIf="g.badgeImage.errors.required">Image is required</div>
                        </div>
                    </div>
                </div>
            </ng-container>
            <ng-container *ngIf="!topicDetails">
                <div class="row form-group px-3" *ngIf="languages.length > 0; else noRecordsFound">
                    <ng-container *ngFor="let item of languages;trackBy: trackByFn; let i = index;">
                        <div class="form-group col-md-6" formGroupName="language" *ngIf="item.identifier !=='en'">
                            <label [for]="item.identifier">{{item.name}}</label>
                            <input type="text" class="form-control" [name]="item.identifier"
                                (keydown)="preventWhiteSpaces($event)" [formControlName]="item.identifier"
                                [id]="item.identifier" placeholder="Topic name in {{item.name}}">
                        </div>
                    </ng-container>
                    <ng-container *ngFor="let item of languages;trackBy: trackByFn; let i = index;">
                        <div class="col-md-12 pt-3" formGroupName="summaryLanguage" *ngIf="item.identifier !=='en'">
                            <label for="summary">Summary in {{item.name}}</label>
                            <ckeditor [formControlName]="item.identifier" [editor]="Editor" [config]="{placeholder: 'Write Summary...'}">
                            </ckeditor>
                        </div>
                    </ng-container>
                    <ng-container *ngFor="let item of languages;trackBy: trackByFn; let i = index;">
                        <!-- <div class="col-md-12 pt-3" formGroupName="descriptionLanguage" *ngIf="item.identifier !=='en'">
                            <label for="Description">Description in {{item.name}}</label>
                            <ckeditor [formControlName]="item.identifier" [editor]="Editor" [config]="{placeholder: 'Write Description...'}">
                            </ckeditor>
                        </div> -->
                        <div class="form-group col-md-6" formGroupName="descriptionLanguage" *ngIf="item.identifier !=='en'">
                            <label [for]="item.identifier">Description in {{item.name}}</label>
                            <input type="text" class="form-control" [name]="item.identifier"
                                (keydown)="preventWhiteSpaces($event)" [formControlName]="item.identifier"
                                [id]="item.identifier" placeholder="Description in {{item.name}}">
                        </div>
                    </ng-container>
                </div>
                <ng-template #noRecordsFound>
                    <p>No languages are available</p>
                </ng-template>
            </ng-container>
        </div>
        <div class="modal-footer">
            <div class="row justify-content-end">
                <div class="pr-3">
                    <label for="activate" class="align-bottom m-0 mt-2 pr-3">Activate</label>
                    <span>
                        <mat-slide-toggle checked formControlName="activate" id="activate" size="small" class="pr-4 mt-2" color='rgb(42,41,92)'></mat-slide-toggle> 
                    </span>
                </div>
                <span class="pr-2 pb-3"><button type="button" class="btn btn-primary pt-1" (click)="add()">
                        {{selectedCategory ? 'Update' : 'Add'}}</button></span>
                <span class="pr-4 pb-3"><button type="button" class="btn btn-secondary pt-1"
                        (click)="close()">Cancel</button></span>
            </div>
            
            <!-- <ngx-spinner id="topicSpinner">
                <p class="text-primary font-weight-bold">Loading please wait..</p>
            </ngx-spinner> -->
        </div>
    </form>
</div>