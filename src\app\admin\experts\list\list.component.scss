
  // @media (max-width: 1300px) {
  //   .custom-add {
  //     display: none;
  //   }
  //   .add-expert {
  //     width: auto;
  //     min-width: 50px;
  //   }
  // }
  
  // @media (min-width: 1301px) {
  //   .add-expert {
  //     width: auto;
  //     // min-width: 120px;
  //     max-width: 135px;
  //   }
  // }
  /* Style the header cells */
::ng-deep .ngx-datatable .datatable-header .datatable-header-cell {
  font-size: 16px !important;
}

/* Style the body cells */
::ng-deep .ngx-datatable .datatable-body .datatable-body-cell {
  font-size: 14px !important;
}
