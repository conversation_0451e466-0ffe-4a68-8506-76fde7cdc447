
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, UntypedFormArray, AbstractControl, UntypedFormControl, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { Title } from '@angular/platform-browser';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { percentageValidator, percentageSumValidator } from 'src/app/carbon/services/customvalidators/percentage.validator';
import { EmissionfactorserviceService } from 'src/app/carbon/services/emissionfactorservice.service';
import { LanguageTranslateService } from 'src/app/carbon/services/language-translate.service';
import { QuestionfromseaService } from 'src/app/carbon/services/questionfromsea.service';
import { DynamicformComponent } from 'src/app/shared/components/dynamicform/dynamicform.component';
import { weightUnits } from 'src/app/shared/constants/constant';
import { DataService } from 'src/app/shared/services/data.service';
import { UnitConversionService } from 'src/app/shared/services/unit-conversion.service';
import { CleaningGeneralInformationService } from '../../services/cleaning-general-information.service';
import { CleaningquestionresultService } from '../../services/cleaningquestionresult.service';
import { CarbonCleaningQuestionsService } from '../../services/carbon-cleaning-questions.service';
import { Router } from '@angular/router';
import { SeasiteService } from 'src/app/sea/services/seasite.service';
import { EmissiontypeService } from 'src/app/carbon/services/emissiontype.service';
const topic = 4;

@Component({
  selector: 'app-energy-equipment-consumption',
  templateUrl: './energy-equipment-consumption.component.html',
  styleUrls: ['./energy-equipment-consumption.component.scss']
})
export class EnergyEquipmentConsumptionComponent {
  @ViewChild('firstTableForm') firstTableForm: DynamicformComponent;
  @ViewChild('fourthTableForm') fourthTableForm: DynamicformComponent;
  @ViewChild('errorPopUP') errorPopUP: TemplateRef<any>;
  @ViewChild('errorPercentage') errorPercentage: TemplateRef<any>;
  submitted = false;
  lastUpdated = new Date();
  transportType: any = [
    { type: 'Diesel', unit: 'litres', consumption: 0 },
    { type: 'Petrol', unit: 'litres', consumption: 0 },
    { type: 'CNG', unit: 'litres', consumption: 0 },
    { type: 'LPG', unit: 'litres', consumption: 0 },
    { type: 'Electricity', unit: 'litres', consumption: 0 },
    { type: 'Biodiesel', unit: 'litres', consumption: 0 },
    { type: 'Biopetrol', unit: 'litres', consumption: 0 },
  ];

  // tslint:disable-next-line:max-line-length
  Units = weightUnits.filter((uni: any) => [5, 6, 7, 8, 3, 4].includes(uni.id) ? true : false).sort((a: any, b: any) => a.order > b.order ? 0 : -1);
  displayedColumns1: string[] = ['name', 'unit', 'consumption', 'co2elb', 'co2emb'];
  firstTabledisplayedColumns: string[] = ['Question', 'Consumed', 'Percentage'];
  secondTabledisplayedColumns: string[] = ['Equ', 'Neu', 'Neb', 'Ned', 'Wna', 'Poe', 'Vwn', 'Hue', 'Noue', 'Nwwd','Cerp','co2elb', 'co2emb'];
  displayedColumns: string[] = ['fuelType', 'unit', 'consumption', 'co2elb'];
  fourthdisplayedColumns: string[] = ['Question', 'fuelType', 'unit', 'consumption', 'co2elb'];
  showHeaders: string[] = ['header-row-first-group'];
  dataSource = new MatTableDataSource<any>();
  dataSource1 = new MatTableDataSource<any>();
  energyForm!: UntypedFormGroup;
  energyFirstForm!: UntypedFormGroup;
  energySecondForm!: UntypedFormGroup;
  energyThirdForm!: UntypedFormGroup;
  energyFourthForm!: UntypedFormGroup;
  isEditableNew = true;
  isLoading = true;
  istype = true;
  questionResult: any = [];
  id: any;
  questionData: any;
  tableQuestion1: any;
  tableQuestion2: any;
  formQuestions: any;
  tableDetails: any;
  tableDetails1: any;
  sortFormQuestions: any;
  language = this.service.languageIdentifier;
  count = 0;
  subscription: Subscription = new Subscription();
  dialogRef: any;
  unit: any = 1;
  un: any = 1;
  showTab = false;
  showTabeFourth = false;
  showTabePaying = false;
  sortTableQuestions = [];
  tableform1!: UntypedFormGroup;
  questionsFromSea: any;
  istoggle = false;
  fourthistoggle = false;
  payingthistoggle = false;
  istoggleValue = undefined;
  fourthistoggleValue = undefined;
  payingistoggleValue = undefined;
  id1: any;
  id3: any; // Add ID for third table
  id4: any; // Add ID for fourth table
  firstTableQuestion: any = [];
  firstTableDetails: any = [];
  firstTableDetailsObj: any;
  firstFormDataSource: any = new MatTableDataSource<any>();
  showFirstTable: boolean = false;
  TableQuestion: any;
  secendTableQuestion: any;
  SecendQuestions: any;
  showsecondTable: boolean = false;
  secondFormDataSource: any = new MatTableDataSource<any>();
  thirdQuestions: any;
  thirdTableDetails: any;
  showThirdTable: boolean = false;
  secondTableDetails: any;
  secondTableDetailsObj: any;
  thirdFormDataSource: any = new MatTableDataSource<any>();
  thirdTableDetailsObj: any;
  thirdTableQuestion: any;
  fourthTableQuestion: any;
  fourthQuestions: any;
  fourthTableDetails: any;
  fourthTableDetailsObj: any;
  fourthFormDataSource: any = new MatTableDataSource<any>();
  showFourthTable: boolean = false;
  patchUnit: string;
  patchUni: string;
  selectedUnit = '1';
  tableQuestion: any;
  sidenavOpened: boolean = false;
  istoggleValueThird: any;
  istoggleValueFourth: any;
  showThirdTab: boolean = false;
  showFourthTab: boolean = false;
  totalElectricityConsumptionReportingPeriod: number = 0;
  locationBasedFactorForTotalCalc: number = 0;
  energyRenewable: number = 0;
  energyNuclear: number = 0;
  energyResidualMix: number = 0;
  percent: number = 0;
  electricEquipmentsDisplayedTopHeader: string[] = ['th4'];
  allEmissionTypes: any[] = [];

  // Add a new property to store sequence 5 questions
  sequenceFiveQuestions: any[] = [];
  percentageValue: number;
  percentageControlId: string;
  emissionValueForWaterConsumption: number = 0;

  constructor(
    private fb: UntypedFormBuilder,
    public service: CleaningGeneralInformationService,
    private emissionService: EmissionfactorserviceService,
    private emissionTypeService: EmissiontypeService,
    private questionService: CarbonCleaningQuestionsService,
    private questionResultService: CleaningquestionresultService,
    private toaster: ToastrService,
    private spinner: NgxSpinnerService,
    private ts: Title,
    private route: Router,
    private data: DataService,
    private modalService: NgbModal,
    private uc: UnitConversionService,
    private questionfromsea: QuestionfromseaService,
    private translate: TranslateService,
    private lts: LanguageTranslateService,
    private site: SeasiteService
  ) {
    translate.use('en');
    translate.setTranslation('en', this.lts.state);
    this.ts.setTitle('Carbon - Energy & Equipment Consumption');
    if (this.service.showCalculation) {
    }
    else {
      this.displayedColumns = ['fuelTypeQuestions', 'fuelType', 'unit', 'consumption', 'co2elb'];
      this.displayedColumns1 = ['name', 'unit', 'consumption'];
      this.fourthdisplayedColumns = ['Question', 'fuelType', 'unit', 'consumption', 'co2elb'];
    }
  }

  ngOnInit(): void {
    this.spinner.show();
    this.energyForm = this.fb.group({
      Rows: this.fb.array([])
    });
    this.tableform1 = this.fb.group({
      Rows: this.fb.array([])
    });

    // Get the emission factor for water consumption
    const emissionValueForWaterConsumption = (this.emissionService.carbonEmissionFactor || []).find(e => e?.description.includes('Water Consumption') || e?.description.includes('water consumption'));
    this.emissionValueForWaterConsumption = emissionValueForWaterConsumption?.value || 0;

    this.getQuestionResult();
    this.getFromSeaQuestions();
    this.getAllEmissionTypes();
  }

  getAllEmissionTypes() {
    this.emissionTypeService.getEmissionTypeAll().subscribe((res: any) => {
      this.allEmissionTypes = res;
    });
  }
  handlePercentageChange(event: {value: number, controlId: string}) {
    this.percentageValue = event.value;
    this.percentageControlId = event.controlId;
    this.percent = event.value;
  }
  getFromSeaQuestions() {
    this.questionfromsea.getQuestionFromSea(this.service.siteId, {
      questionId: ['EM-TDZ']
    }).subscribe((res: any) => {
      this.questionsFromSea = res;
      this.getQuestionResult();
    }, (err: any) => {
    });
  }
  getUnit(e?: any) {
    if (e) {
      this.patchUnit = weightUnits.find(unit => unit.value == e).symbol;
    } else {

      this.patchUnit = weightUnits.find(unit => unit.id == 5).symbol;
    }
  }
  // selectUnit(e) {
  //   this.selectedUnit = e;
  //   this.getUnit(e);
  //   this.tableDetails.map((detail: any, i: number) => this.onUnitChange(i));
  // }
  getUni(e?: any) {
    if (e) {
      this.patchUni = weightUnits.find(unit => unit.value == e).symbol;
    } else {

      this.patchUni = weightUnits.find(unit => unit.id == 5).symbol;
    }
  }
  selectUni(e) {
    this.selectedUnit = e;
    this.getUni(e);
    this.tableDetails.map((detail: any, i: number) => this.onUnitChange(i));
  }
  selectUnit(e: any) {
    this.tableDetails.map((detail: any, i: number) => {
      const myForm = (this.energyThirdForm.get('Rows') as UntypedFormArray).at(i);
      if (detail?.unitdropDownType?.id !== 1) {
        let obj: any;
        if (detail.isUserCanChangeUnit) {

        } else {
          obj = (detail?.units || []).find((unit: any) => this.unit === unit?.value);
          myForm.patchValue({
            unit: obj.value
          });
        }
      }
      this.onUnitChange(i);
    });
  }
  getQuestionResult() {
    this.isLoading = true;
    const payload = {
      topic,
      site: this.service.siteId,
      date: this.service.endDate,
    };
    const answer$ = this.questionResultService.getCleaningQuestionsResultsById(payload).subscribe((res: any) => {
      this.questionResult = res.data;
      res.data.map((xa: any) => {
        if (xa.question_format === 1 && xa.sequence === 2) {
          this.id = xa.id;
        }
        if (xa.question_format === 1 && xa.sequence === 1) {
          this.id1 = xa.id;
        }
        if (xa.question_format === 1 && xa.sequence === 3) {
          this.id3 = xa.id;
        }
        if (xa.question_format === 1 && xa.sequence === 4) {
          this.id4 = xa.id;
        }
      });
      console.log("this.questionResult : ", this.questionResult);
      
      this.spinner.hide();
      this.getQuestions(topic);
    },
      err => {
        this.spinner.hide();
        this.getQuestions(topic);
      });
    this.subscription.add(answer$);
  }
  getQuestions(type) {
    const questions$ = this.questionService.getCleaningQuestionByTopicId(type).subscribe(data => {
      this.spinner.hide();
      this.questionData = data;
      // filter questions
      this.firstTableQuestion = (this.questionData || []).filter(q => q.sequence === 1 && q.is_active);
      this.secendTableQuestion = (this.questionData || []).filter(q => q.sequence == 2 && q.is_active);
      this.thirdTableQuestion = (this.questionData || []).filter(q => q.sequence == 3 && q.is_active);
      this.fourthTableQuestion = (this.questionData || []).filter(q => q.sequence == 4 && q.is_active);
      this.tableQuestion = (this.questionData || []).filter(q => q.question_format === 1 && q.is_active);
      this.formQuestions = (this.questionData || []).filter(q => q.question_format === 0 && q.is_active);
      console.log("this.formQuestions: ", this.formQuestions);
      
      // Filter formQuestions to only include sequence 0 and 1
      const sortFormQuestions = this.formQuestions
        .filter(q => q.sequence === 0 || q.sequence === 1)
        .sort((a: any, b: any) => {
          return a.sequence > b.sequence ? 0 : -1;
        });
        
        this.sortFormQuestions = this.FilterTableQueFromSea(sortFormQuestions);
        console.log("this.sortFormQuestions: ", sortFormQuestions);
      
      // Filter for sequence 5 questions
      this.sequenceFiveQuestions = this.formQuestions
        .filter(q => q.sequence === 5 && q.is_active)
        .sort((a: any, b: any) => {
          return a.sequence > b.sequence ? 0 : -1;
        });

      // Apply the same filtering if needed
      this.sequenceFiveQuestions = this.FilterTableQueFromSea(this.sequenceFiveQuestions);

      // tslint:disable-next-line:no-shadowed-variable
      this.sortTableQuestions = this.tableQuestion.sort((a: any, b: any) => {
        return a.sequence > b.sequence ? 0 : -1;
      });

      if (this.firstTableQuestion.length > 0) {
        this.TableQuestion = (this.firstTableQuestion || []).filter(q => q.question_format === 0 && q.is_active);

        this.initiateFirstTable(this.firstTableQuestion, this.TableQuestion);
      }
      if (this.secendTableQuestion.length > 0) {
        this.SecendQuestions = (this.secendTableQuestion || []).filter(q => q.question_format === 0 && q.is_active);
        this.initiateSecendTable(this.secendTableQuestion, this.SecendQuestions);
      }
      if (this.thirdTableQuestion.length > 0) {
        this.thirdQuestions = (this.thirdTableQuestion || []).filter(q => q.question_format === 0 && q.is_active);
        this.initiateThirdTable(this.thirdTableQuestion, this.thirdQuestions);
      }
      if (this.fourthTableQuestion.length > 0) {
        this.fourthQuestions = (this.fourthTableQuestion || []).filter(q => q.question_format === 0 && q.is_active);
        this.initiateFourthTable(this.fourthTableQuestion, this.fourthQuestions);
      }
      this.isLoading = false;
      if (this.tableQuestion.length > 0) {
        this.initiateTable();
      }
      this.triggerWaterTableCalculations();
    },
      err => {
        this.isLoading = false;
        this.spinner.hide();
        this.toaster.error('Failed to load questions');
      });
    this.subscription.add(questions$);
  }

  initiateFirstTable(firstQuestionData: any, tableQuestion?: any) {
    this.spinner.show();
    this.firstTableDetails = firstQuestionData[0].options
    this.firstTableDetailsObj = firstQuestionData[0];
    
    this.energyFirstForm = this.fb.group({
      Rows: this.fb.array((this.firstTableDetails || []).map(
        (val: any, index: number) => this.checkFirstQuestionResult(val)
      )) // end of fb array
    });
    this.firstFormDataSource = new MatTableDataSource((this.energyFirstForm.get('Rows') as UntypedFormArray).controls);
    setTimeout(() => {
      (this.energyFirstForm.value.Rows || []).forEach((val, index) => {
      });
      this.firstFormDataSource = new MatTableDataSource((this.energyFirstForm.get('Rows') as UntypedFormArray).controls);
      this.spinner.hide();
    }, 300);

    this.getUnit();
    this.getUni();
    this.showFirstTable = true;

  }
  initiateSecendTable(secendQuestionData: any, tableQuestion?: any) {
    this.spinner.show();

    this.secondTableDetails = secendQuestionData[0].options;
    this.secondTableDetailsObj = secendQuestionData[0];
    this.energySecondForm = this.fb.group({
      Rows: this.fb.array((this.secondTableDetails || []).map(

        (val: any, index: number) => this.checkSecondQuestionResult(val, index))) // end of fb array

    });
    this.secondFormDataSource = new MatTableDataSource((this.energySecondForm.get('Rows') as UntypedFormArray).controls);
    setTimeout(() => {
      (this.energySecondForm.value.Rows || []).forEach((val, index) => {
      });
      this.secondFormDataSource = new MatTableDataSource((this.energySecondForm.get('Rows') as UntypedFormArray).controls);
      this.spinner.hide();
    }, 300);
    this.spinner.hide();
    this.showsecondTable = true;

  }
  initiateThirdTable(thirdQuestionData: any, tableQuestion?: any) {
    this.spinner.show();
    this.thirdTableDetails = thirdQuestionData[0].options
    this.thirdTableDetailsObj = thirdQuestionData[0];
    this.energyThirdForm = this.fb.group({
      Rows: this.fb.array((this.thirdTableDetails || []).map(
        (val: any, index: number) => this.checkThirdQuestionResult(val, index)
      )) // end of fb array
    });

    // Set toggle value for third table
    let qr = this.questionResult.filter(q => q.question === this.thirdTableDetailsObj.id && q.sequence === this.thirdTableDetailsObj.sequence && q.question_format === this.thirdTableDetailsObj.question_format);
    if (qr.length > 0) {
      try {
        this.istoggleValueThird = JSON.parse(qr[qr.length-1].unit).toggle;
      } catch (e) {
        this.istoggleValueThird = qr[qr.length-1].unit.toggle;
      }
      // Automatically show/hide the form based on saved toggle value
      this.changeYesOrNoThird(this.istoggleValueThird);
    }

    this.thirdFormDataSource = new MatTableDataSource((this.energyThirdForm.get('Rows') as UntypedFormArray).controls);
    setTimeout(() => {
      (this.energyThirdForm.value.Rows || []).forEach((val, index) => {
      });
      this.thirdFormDataSource = new MatTableDataSource((this.energyThirdForm.get('Rows') as UntypedFormArray).controls);
      this.spinner.hide();
    }, 300);

    this.getUnit();
    this.getUni();
    this.showThirdTable = true;

  }
  initiateFourthTable(fourthQuestionData: any, tableQuestion?: any) {
    this.spinner.show();
    this.fourthTableDetails = fourthQuestionData[0].options
    this.fourthTableDetailsObj = fourthQuestionData[0];
    this.energyFourthForm = this.fb.group({
      Rows: this.fb.array((this.fourthTableDetails || []).map(
        (val: any, index: number) => this.checkFourthQuestionResult(val, index)
      )) // end of fb array
    });

    // Set toggle value for fourth table
    let qr = this.questionResult.filter(q => q.question === this.fourthTableDetailsObj.id && q.sequence === this.fourthTableDetailsObj.sequence && q.question_format === this.fourthTableDetailsObj.question_format);
    if (qr.length > 0) {
      try {
        this.istoggleValueFourth = JSON.parse(qr[qr.length-1].unit).toggle;
      } catch (e) {
        this.istoggleValueFourth = qr[qr.length-1].unit.toggle;
      }
      // Automatically show/hide the form based on saved toggle value
      this.changeYesOrNoFourth(this.istoggleValueFourth);
    }

    this.fourthFormDataSource = new MatTableDataSource((this.energyFourthForm.get('Rows') as UntypedFormArray).controls);

    setTimeout(() => {
      (this.energyFourthForm.value.Rows || []).forEach((val, index) => {
      });
      this.fourthFormDataSource = new MatTableDataSource((this.energyFourthForm.get('Rows') as UntypedFormArray).controls);
      this.spinner.hide();
    }, 300);

    this.showFourthTable = true;

  }
  checkFirstQuestionResult(question) {
    let qr = this.questionResult.filter(q => q.question === this.firstTableDetailsObj.id && q.sequence === this.firstTableDetailsObj.sequence && q.question_format === this.firstTableDetailsObj.question_format);

    if (qr.length > 0) { 
      let parsedQuestionResult = JSON.parse(qr[qr.length-1].answer || '[]');
console.log("parsedQuestionResult: ", parsedQuestionResult);

      return this.fb.group({
        name: new UntypedFormControl(question.name),
        toolTip: new UntypedFormControl(question.description),
        energyConsumed: new UntypedFormControl(parsedQuestionResult[0]?.energyConsumed),
        // percentage: new UntypedFormControl(),
        percentage: new UntypedFormControl(
          parsedQuestionResult[0]?.percentage || 0, 
          [
            Validators.min(0), 
            Validators.max(100), 
          ]
        ),
        // co2e: new UntypedFormControl(),
        // noOfSubEmp: new UntypedFormControl(),
        // totalSubKm: new UntypedFormControl(),
        // co2esub: new UntypedFormControl(),
        // group: new UntypedFormControl(question.group),
        question_id: new UntypedFormControl(question.question_id),
        // tslint:disable-next-line:triple-equals
        isRequired: new UntypedFormControl(question.isRequired),
        isDisabled: new UntypedFormControl(question.isDisabled),
        isExpanded: new UntypedFormControl(false),
        icon: new UntypedFormControl(false),
        level: new UntypedFormControl(question.level),
        priority: new UntypedFormControl(question.priority)
      });
    }
    else {
      // question.options[0].value = true;
      return this.fb.group({
        name: new UntypedFormControl(question.name),
        toolTip: new UntypedFormControl(question.description),
        energyConsumed: new UntypedFormControl(question.options),
        // percentage: new UntypedFormControl(),
        percentage: new UntypedFormControl(
          null, 
          [
            Validators.min(0), 
            Validators.max(100), 
          ]
        ),
        // co2e: new UntypedFormControl(),
        // noOfSubEmp: new UntypedFormControl(),
        // totalSubKm: new UntypedFormControl(),
        // co2esub: new UntypedFormControl(),
        // group: new UntypedFormControl(question.group),
        question_id: new UntypedFormControl(question.question_id),
        // tslint:disable-next-line:triple-equals
        isRequired: new UntypedFormControl(question.isRequired),
        isDisabled: new UntypedFormControl(question.isDisabled),
        isExpanded: new UntypedFormControl(false),
        icon: new UntypedFormControl(false),
        level: new UntypedFormControl(question.level),
        priority: new UntypedFormControl(question.priority)
      });
    }
  }
  checkSecondQuestionResult(question, i) {
    let qr = this.questionResult.filter(q => q.question === this.secondTableDetailsObj.id && q.sequence === this.secondTableDetailsObj.sequence && q.question_format === this.secondTableDetailsObj.question_format);
    if (qr.length > 0) { 
      let parsedQuestionResult = JSON.parse(qr[qr.length-1].answer || '[]');

      return this.fb.group({
        name: new UntypedFormControl(question.name),
        toolTip: new UntypedFormControl(question.description),
        Neu: new UntypedFormControl(parsedQuestionResult[i]?.Neu),
        Neb: new UntypedFormControl(parsedQuestionResult[i]?.Neb),
        Ned: new UntypedFormControl(parsedQuestionResult[i]?.Ned),
        Wna: new UntypedFormControl(parsedQuestionResult[i]?.Wna),
        Poe: new UntypedFormControl(parsedQuestionResult[i]?.Poe),
        Vwn: new UntypedFormControl(parsedQuestionResult[i]?.Vwn),
        Hue: new UntypedFormControl(parsedQuestionResult[i]?.Hue),
        Noue: new UntypedFormControl(parsedQuestionResult[i]?.Noue),
        Nwwd: new UntypedFormControl(parsedQuestionResult[i]?.Nwwd),
        Cerp: new UntypedFormControl(parsedQuestionResult[i]?.Cerp),
        Wcrp: new UntypedFormControl(parsedQuestionResult[i]?.Wcrp),
        co2elb: new UntypedFormControl(parsedQuestionResult[i]?.co2elb),
        co2emb: new UntypedFormControl(parsedQuestionResult[i]?.co2emb),
        co2eWater: new UntypedFormControl(parsedQuestionResult[i]?.co2eWater),
        question_id: new UntypedFormControl(question.question_id),
        isRequired: new UntypedFormControl(question.isRequired),
        isDisabled: new UntypedFormControl(question.isDisabled),
        isExpanded: new UntypedFormControl(false),
        icon: new UntypedFormControl(false),
        level: new UntypedFormControl(question.level),
        priority: new UntypedFormControl(question.priority),
      });
    }
    else {
      return this.fb.group({
        name: new UntypedFormControl(question.name),
        toolTip: new UntypedFormControl(question.description),
        Neu: new UntypedFormControl(),
        Neb: new UntypedFormControl(),
        Ned: new UntypedFormControl(),
        Wna: new UntypedFormControl(),
        Poe: new UntypedFormControl(),
        Vwn: new UntypedFormControl(),
        Hue: new UntypedFormControl(),
        Noue: new UntypedFormControl(),
        Nwwd: new UntypedFormControl(),
        Cerp: new UntypedFormControl(),
        Wcrp: new UntypedFormControl(),
        co2elb: new UntypedFormControl(),
        co2emb: new UntypedFormControl(),
        co2eWater: new UntypedFormControl(),
        question_id: new UntypedFormControl(question.question_id),
        isRequired: new UntypedFormControl(question.isRequired),
        isDisabled: new UntypedFormControl(question.isDisabled),
        isExpanded: new UntypedFormControl(false),
        icon: new UntypedFormControl(false),
        level: new UntypedFormControl(question.level),
        priority: new UntypedFormControl(question.priority),
      });
    }
  }
  checkThirdQuestionResult(question, i) {
    let qr = this.questionResult.filter(q => q.question === this.thirdTableDetailsObj.id && q.sequence === this.thirdTableDetailsObj.sequence && q.question_format === this.thirdTableDetailsObj.question_format);
    
    if (qr.length > 0) { 
      let parsedQuestionResult = JSON.parse(qr[qr.length-1]?.answer || '[]');

      return this.fb.group({
        name: new UntypedFormControl(question.name),
        unit: new UntypedFormControl(parsedQuestionResult[i]?.unit || question?.units[0]?.value),
        isUserCanChangeUnit: new UntypedFormControl(question?.isUserCanChangeUnit),
        question_id: new UntypedFormControl(question.question_id),
        // unit: new FormControl(question.name === 'Electricity' ? 'kWh' : question.unit || 1),
        consumption: new UntypedFormControl(parsedQuestionResult[i]?.consumption || null),
        co2elb: new UntypedFormControl(parsedQuestionResult[i]?.co2elb || null),
      });
    }
    else {
      // question.options[0].value = true;
      return this.fb.group({
        name: new UntypedFormControl(question.name),
        unit: new UntypedFormControl(question?.units?.length ? question.units[0].value : null),
        isUserCanChangeUnit: new UntypedFormControl(question?.isUserCanChangeUnit),
        question_id: new UntypedFormControl(question.question_id),
        // unit: new FormControl(question.name === 'Electricity' ? 'kWh' : question.unit || 1),
        consumption: new UntypedFormControl(),
        co2elb: new UntypedFormControl(),
        // co2e: new UntypedFormControl(),
      });
    }
  }
  checkFourthQuestionResult(question, i) {

    let qr = this.questionResult.filter(q => q.question === this.fourthTableDetailsObj.id && q.sequence === this.fourthTableDetailsObj.sequence && q.question_format === this.fourthTableDetailsObj.question_format);
    if (qr.length > 0) { 
      let parsedQuestionResult = JSON.parse(qr[qr.length-1]?.answer || '[]');

      return this.fb.group({
        name: new UntypedFormControl(question.name),
        fuelType: new UntypedFormControl(parsedQuestionResult[i]?.fuelType || question.fuelType),
        unit: new UntypedFormControl(parsedQuestionResult[i]?.unit || question?.units?.[0]?.value),
        isUserCanChangeUnit: new UntypedFormControl(question?.isUserCanChangeUnit),
        question_id: new UntypedFormControl(question.question_id),
        // unit: new FormControl(question.name === 'Electricity' ? 'kWh' : question.unit || 1),
        consumption: new UntypedFormControl(parsedQuestionResult[i]?.consumption || null),
        // co2e: new UntypedFormControl(),
        co2elb: new UntypedFormControl(parsedQuestionResult[i]?.co2elb || null),
      });
    }
    else {
      // question.options[0].value = true;
      return this.fb.group({
        name: new UntypedFormControl(question.name),
        fuelType: new UntypedFormControl(question.fuelType),
        unit: new UntypedFormControl(question?.units?.[0]?.value),
        isUserCanChangeUnit: new UntypedFormControl(question?.isUserCanChangeUnit),
        question_id: new UntypedFormControl(question.question_id),
        // unit: new FormControl(question.name === 'Electricity' ? 'kWh' : question.unit || 1),
        consumption: new UntypedFormControl(),
        // co2e: new UntypedFormControl(),
        co2elb: new UntypedFormControl(),
      });
    }
  }
  initiateTable() {
    this.tableDetails = this.tableQuestion.options || [];
    this.energyForm = this.fb.group({
      Rows: this.fb.array(this.tableDetails.map(controller => this.checkQuestionResult(controller)
      ))
    });
    this.isLoading = false;
    this.spinner.hide();
    this.dataSource = new MatTableDataSource((this.energyForm.get('Rows') as UntypedFormArray).controls);
    const filterPredicate = this.dataSource.filterPredicate;
    this.dataSource.filterPredicate = (data: AbstractControl, filter) => {
      return filterPredicate.call(this.dataSource, data.value, filter);
    };
  }
  checkQuestionResult(question) {
    const options = (this.tableQuestion2.language[this.language] ?
      this.tableQuestion2.language[this.language].options : this.tableQuestion2.options)
      .find((option: any) => option.question_id === question.question_id);
    question.name = options.name;
    question.description = options.description;
    if (this.questionResult.length > 0) {
      // tslint:disable-next-line:max-line-length
      const result = (this.questionResult || []).filter(q => q.question === this.tableQuestion2.id && q.sequence === this.tableQuestion2.sequence && q.question_format === this.tableQuestion2.question_format);
      try {
        this.selectedUnit = JSON.parse(result[0].unit).unit;
      } catch (e) {
        this.selectedUnit = result[0].unit.unit;
      }
      // try {
      //   this.unit = JSON.parse(result[0].unit).unit;
      // } catch (e) {
      //   this.unit = result[0].unit.unit;
      // }
      let obj = [];
      try {
        obj = JSON.parse(result[0]?.answer);
      } catch (e) {
        obj = [];
      }
      const answer = (obj || []).filter(r => r.question_id === question.question_id);
      if (result.length > 0) {
        return this.fb.group({
          name: new UntypedFormControl(question?.name),
          unit: new UntypedFormControl(answer[0]?.unit),
          isUserCanChangeUnit: new UntypedFormControl(answer[0]?.isUserCanChangeUnit),
          question_id: new UntypedFormControl(question.question_id),
          // unit: new FormControl(answer[0].name === 'Electricity' ? 'kWh' : answer[0].unit || 1),
          consumption: new UntypedFormControl(answer[0]?.consumption),
          co2e: new UntypedFormControl(answer[0]?.co2e),
        });
      } else {
        return this.fb.group({
          name: new UntypedFormControl(question.name),
          unit: new UntypedFormControl(question.units[0]?.value),
          question_id: new UntypedFormControl(question.question_id),
          isUserCanChangeUnit: new UntypedFormControl(question?.isUserCanChangeUnit),
          // unit: new FormControl(question.name === 'Electricity' ? 'kWh' : question.unit || 1),
          consumption: new UntypedFormControl(),
          co2e: new UntypedFormControl(),
        });
      }
    } else {
      return this.fb.group({
        name: new UntypedFormControl(question.name),
        unit: new UntypedFormControl(question.units[0]?.value),
        isUserCanChangeUnit: new UntypedFormControl(question?.isUserCanChangeUnit),
        question_id: new UntypedFormControl(question.question_id),
        // unit: new FormControl(question.name === 'Electricity' ? 'kWh' : question.unit || 1),
        consumption: new UntypedFormControl(),
        co2e: new UntypedFormControl(),
      });
    }
  }
 locationBasedFactor(){
    let residualMix = 0;
    let carpooling = 0;
    let renewable = 0;
    let nuclear = 0;

    for (const item of this.emissionService.carbonEmissionFactor || []) {
      switch (item.description) {
        case 'Electricity - residual mix':
          residualMix = item.value;
          break;
        case 'Carpooling':
          carpooling = item.value;
          break;
        case 'Electricity - renewable':
          renewable = item.value;
          break;
        case 'Electricity - nuclear':
          nuclear = item.value;
          break;
      }
    }
    const factor = (residualMix * carpooling / 100) + ((renewable + nuclear) * (this.percent / 100));
    this.locationBasedFactorForTotalCalc = factor;
    this.energyRenewable = renewable;
    this.energyNuclear = nuclear;
    this.energyResidualMix = residualMix;
 }
 onSearchChange(value: any, index: any, tableDetails: any[], form?: UntypedFormGroup) {
  const currentRow = tableDetails[index];
  if (!currentRow) return; // 💡 optional safe guard
  this.emissionService.carbonEmissionFactor.filter(e => e.description == "Electricity - renewable" || e.description == "Electricity - nuclear")
  const emissionType = currentRow.emissionType || {};
  const emissionValue = (this.emissionService.carbonEmissionFactor || []).find(
    (e: any) => e.emission_type === emissionType.id
  );

  // You should also receive the form as an argument or determine it based on context
  const myForm = (form.get('Rows') as UntypedFormArray).at(index);
  if(myForm?.get('Neu') && myForm?.get('Poe') && myForm?.get('Hue') && myForm?.get('Nwwd')) {
    const electricyConsumption = (myForm.value?.Neu || 0) * (myForm.value?.Poe || 0) * (myForm.value?.Hue || 0) * (myForm.value?.Nwwd || 0);
    myForm.patchValue({
      Cerp: electricyConsumption,
    });

    this.locationBasedFactor();
    
    const calLocationBased = (electricyConsumption * (this.locationBasedFactorForTotalCalc || 0)) / 1000;
    if (calLocationBased) {
      myForm.patchValue({
        co2elb: calLocationBased.toFixed(3),
      });
    }

    const calMarketBased = (electricyConsumption * (this.energyRenewable + this.energyNuclear)) + (((electricyConsumption - this.percent) * this.energyResidualMix) / 1000);
    if (calMarketBased) {
      myForm.patchValue({
        co2emb: calMarketBased.toFixed(3),
      });
    }
    
    this.calculateReportingTotal();
    this.calculateLocalTotal();
    this.calculateMarketTotal();
  }
  
  let unitValue = value;
  const unit = this.selectedUnit;

  if (currentRow?.unitdropDownType?.id !== 1) {
    let obj: any;
    if (currentRow.isUserCanChangeUnit) {
      obj = (currentRow?.units || []).find((unit: any) => myForm.value.unit == unit?.value);
      unitValue *= obj?.conversionValue || 1;
    } else {
      obj = (currentRow?.units || []).find((unit: any) => this.unit == unit?.value);
      unitValue *= obj?.conversionValue || 1;
    }
  }

  const cal = (unitValue * (emissionValue?.value || 0)) / 1000;
  if (cal) {
    myForm.patchValue({
      co2e: cal.toFixed(3),
      co2elb: cal.toFixed(3),
    });
  }

  if(myForm?.get('Neu') && myForm?.get('Vwn') && myForm?.get('Noue') && myForm?.get('Nwwd')) {
    this.onSearchChangeWaterTable(index, form);
  }
}
onSearchChangeWaterTable(index: any, form?: UntypedFormGroup){
  const myForm = (form.get('Rows') as UntypedFormArray).at(index);
  const waterConsumption = (myForm.value?.Neu || 0) * (myForm.value?.Vwn || 0) * (myForm.value?.Noue || 0) * (myForm.value?.Nwwd || 0);
  
  myForm.patchValue({
    Wcrp: waterConsumption,
  });
  
  if(!this.emissionValueForWaterConsumption){
    const emissionValueForWaterConsumption = (this.emissionService.carbonEmissionFactor || []).find(e => e?.description.includes('Water Consumption') || e?.description.includes('water consumption'));
    this.emissionValueForWaterConsumption = emissionValueForWaterConsumption?.value || 0;
  }
  
  const calWater = (myForm.value?.Wcrp || 0) * this.emissionValueForWaterConsumption;
  myForm.patchValue({
    co2eWater: calWater
  });
}
  // // submit() {
  // //   if (this.count === 0) {
  // //     this.submitted = true;
  // //     this.count++;
  // //   } else {
  // //     this.dynamicForm.onSubmit();
  // //   }
  // // }
  submit() {
    // this.submitTable();
    // this.submitTable1();
    // this.submitThirdTable();
    // this.submitFourthTable();

    if (this.showFirstTable && this.firstTableForm) {
      this.firstTableForm.onSubmit();
    }
    if (this.showFourthTab && this.fourthTableForm) {
      this.fourthTableForm.onSubmit();
    }

    // this.route.navigate([`carbon-cleaning/${this.service.siteId}/product`]);
  }


  submitTable() {
    if (this.secondTableDetailsObj) {
      const payload: any = {
        answer: [...this.energySecondForm.value.Rows],
        site: this.service.siteId,
        start_date: this.service.startDate,
        end_date: this.service.endDate,
        topic: 4,
        question: this.secondTableDetailsObj.id,
        question_format: this.secondTableDetailsObj.question_format,
        sequence: this.secondTableDetailsObj.sequence,
        // tslint:disable-next-line:radix
        unit: { unit: parseInt(this.unit) },
        service: "Cleaning Services"
      };
      this.spinner.show();
      
      if (this.id) {
        const response1$ = this.questionResultService.updateQuestionsResults(payload, this.id).subscribe((res: any) => {
          this.spinner.hide();
          this.data.updateReload(true);
          this.toaster.success('Data saved successfully');
        },
          err => {
            this.spinner.hide();
            this.isLoading = false;
            this.toaster.error('Failed to load questions');
          });
        // this.subscription.add(response1$);
      } else {
        const response2$ = this.questionResultService.createQuestionResults(payload).subscribe((res: any) => {
          this.id = res.id;
          this.spinner.hide();
          this.data.updateReload(true);
          this.toaster.success('Data saved successfully');
        },
          err => {
            this.spinner.hide();
            this.toaster.error('Failed to load questions');
          });
        // this.subscription.add(response2$);
      }
    }
  }
  submitTable1() {
    if (this.firstTableDetailsObj) {
      const payload = {
        answer: [...this.energyFirstForm.value.Rows],
        site: this.service.siteId,
        start_date: this.service.startDate,
        end_date: this.service.endDate,
        topic: 4,
        question: this.firstTableDetailsObj.id,
        question_format: this.firstTableDetailsObj.question_format,
        sequence: this.firstTableDetailsObj.sequence,
        // tslint:disable-next-line:radix
        unit: { toggle: this.istoggleValue },
        units: { toggle: this.fourthistoggleValue },
        paying: { toggle: this.payingistoggleValue },
        service: "Cleaning Services"
      };
      this.spinner.show();
      
      if (this.id1) {
        const response1$ = this.questionResultService.updateQuestionsResults(payload, this.id1).subscribe((res: any) => {
          // this.submitTable();
          this.spinner.hide();
          // this.data.updateReload(true);
          this.toaster.success('Data saved successfully');
        },
          err => {
            this.spinner.hide();
            this.isLoading = false;
            this.toaster.error('Failed to load questions');
          });
        // this.subscription.add(response1$);
      } else {
        const response2$ = this.questionResultService.createQuestionResults(payload).subscribe((res: any) => {
          // this.submitTable();
          this.id1 = res.id;
          this.spinner.hide();
          // this.data.updateReload(true);
          this.toaster.success('Data saved successfully');
        },
          err => {
            this.spinner.hide();
            this.toaster.error('Failed to load questions');
          });
        // this.subscription.add(response2$);
      }
    }
  }
  submitThirdTable(){  
    if (this.thirdTableDetailsObj && this.energyThirdForm) {
      const payload = {
        answer: [...this.energyThirdForm.value.Rows],
        site: this.service.siteId,
        start_date: this.service.startDate,
        end_date: this.service.endDate,
        topic: 4,
        question: this.thirdTableDetailsObj.id,
        question_format: this.thirdTableDetailsObj.question_format,
        sequence: this.thirdTableDetailsObj.sequence,
        // tslint:disable-next-line:radix
        unit: { toggle: this.istoggleValueThird },
        service: "Cleaning Services"
      };
      
      this.spinner.show();
      
      if (this.id3) {
        const response1$ = this.questionResultService.updateQuestionsResults(payload, this.id3).subscribe((res: any) => {
          this.spinner.hide();
          this.data.updateReload(true);
          this.toaster.success('Third table data saved successfully');
        },
          err => {
            this.spinner.hide();
            this.isLoading = false;
            this.toaster.error('Failed to save third table data');
          });
        this.subscription.add(response1$);
      } else {
        const response2$ = this.questionResultService.createQuestionResults(payload).subscribe((res: any) => {
          this.id3 = res.id;
          this.spinner.hide();
          this.data.updateReload(true);
          this.toaster.success('Third table data saved successfully');
        },
          err => {
            this.spinner.hide();
            this.toaster.error('Failed to save third table data');
          });
        this.subscription.add(response2$);
      }
    }
  }
  submitFourthTable(){
    if (this.fourthTableDetailsObj) {
      const payload = {
        answer: [...this.energyFourthForm.value.Rows],
        site: this.service.siteId,
        start_date: this.service.startDate,
        end_date: this.service.endDate,
        topic: 4,
        question: this.fourthTableDetailsObj.id,
        question_format: this.fourthTableDetailsObj.question_format,
        sequence: this.fourthTableDetailsObj.sequence,
        // tslint:disable-next-line:radix
        unit: { toggle: this.istoggleValueFourth },
        units: { toggle: this.fourthistoggleValue },
        paying: { toggle: this.payingistoggleValue },
        service: "Cleaning Services"
      };
      this.spinner.show();
      
      if (this.id4) {
        const response1$ = this.questionResultService.updateQuestionsResults(payload, this.id4).subscribe((res: any) => {
          // this.submitTable();
          this.spinner.hide();
          // this.data.updateReload(true);
          this.toaster.success('Data saved successfully');
        },
          err => {
            this.spinner.hide();
            this.isLoading = false;
            this.toaster.error('Failed to load questions');
          });
        // this.subscription.add(response1$);
      } else {
        const response2$ = this.questionResultService.createQuestionResults(payload).subscribe((res: any) => {
          // this.submitTable();
          this.id4 = res.id;
          this.spinner.hide();
          // this.data.updateReload(true);
          this.toaster.success('Data saved successfully');
        },
          err => {
            this.spinner.hide();
            this.toaster.error('Failed to load questions');
          });
        // this.subscription.add(response2$);
      }
    }
  }
  jsonFormValue(event) {
    if (this.firstTableForm?.form?.invalid) {
      return;
    }
    // if (this.tableform1.invalid) {
    //   return;
    // }
    // if (!this.istoggleValue) {
    //   return;
    // }
    // if (!this.fourthistoggleValue) {
    //   return;
    // }
    // if (!this.payingistoggleValue) {
    //   return;
    // }
    if (this.showTab) {
      if (this.checkForValidation(event)) {
        this.showErrorMessage();
        return;
      }
    } else {
      this.resetField('consumption');
      this.resetField('co2e');
    }
    this.submitTable1();
    // tslint:disable-next-line:prefer-const
    let payload = [];

    // Use event.calc which contains the actual questions with calculated values
    for (const [key, value] of Object.entries(event?.formvalue)) {
      const obj: any = {};
      event.calc.map((question: any) => {
        // tslint:disable-next-line:radix
        if (parseInt(key) === question.id) {
          obj.answer = { formValue: value, tCO2eLB: question.tCO2eLB, tCO2eMB: question.tCO2eMB },
            obj.site = this.service.siteId;
          obj.start_date = this.service.startDate;
          obj.end_date = this.service.endDate;
          obj.topic = 4;     // Energy
          obj.question = question.id;
          obj.question_format = question.question_format;
          obj.sequence = question.sequence;
          obj.service = "Food & Catering Services",
            obj.isApprovedBy = true
        }
        // tslint:disable-next-line:radix
        if (parseInt(key) === question.id && question.unit === null && question.emission_type != null) {
          obj.answer = { ...obj.answer, unit: event.unit };
        }
      });
      if (this.questionResult.length > 0) {
        this.questionResult.map((result: any) => {
          // tslint:disable-next-line:radix
          if (parseInt(key) === result.question) {
            obj.id = result.id;
          }
        });
      }
      payload.push(obj);
    }
    const response3$ = this.questionResultService.createBulkQuestions(payload).subscribe((res: any) => {
      // this.service.isAprroved();
      this.spinner.hide();
    },
      err => {
        this.spinner.hide();
        // this.isLoading = false;
        this.toaster.error('Failed to upload questions');
      });
    // this.subscription.add(response3$);
  }

  jsonFormValueFourthTable(event) {
    if (this.fourthTableForm?.form?.invalid) {
      return;
    }

    // Use event.calc which contains the actual questions with calculated values
    let payload = [];
    for (const [key, value] of Object.entries(event?.formvalue)) {
      const obj: any = {};
      event.calc.map((question: any) => {
        // tslint:disable-next-line:radix
        if (parseInt(key) === question.id) {
          obj.answer = { formValue: value, tCO2eLB: question.tCO2eLB, tCO2eMB: question.tCO2eMB },
            obj.site = this.service.siteId;
          obj.start_date = this.service.startDate;
          obj.end_date = this.service.endDate;
          obj.topic = 4;     // Energy
          obj.question = question.id;
          obj.question_format = question.question_format;
          obj.sequence = question.sequence;
          obj.service = "Cleaning Services", // Note: Different service name for cleaning
            obj.isApprovedBy = true
        }
        // tslint:disable-next-line:radix
        if (parseInt(key) === question.id && question.unit === null && question.emission_type != null) {
          obj.answer = { ...obj.answer, unit: event.unit };
        }
      });
      if (this.questionResult.length > 0) {
        this.questionResult.map((result: any) => {
          // tslint:disable-next-line:radix
          if (parseInt(key) === result.question) {
            obj.id = result.id;
          }
        });
      }
      payload.push(obj);
    }

    const response$ = this.questionResultService.createBulkQuestions(payload).subscribe((res: any) => {
      this.spinner.hide();
    },
      err => {
        this.spinner.hide();
        this.toaster.error('Failed to upload fourth table questions');
      });
    // this.subscription.add(response$);
  }

  checkForValidation(event: any): boolean {
    for (const question of this.sortFormQuestions) {
      if (question.sequence === 19) {
        for (const [key, value] of Object.entries(event.formvalue)) {
          // tslint:disable-next-line:radix
          if (question.id === parseInt(key) && value === 'Yes') {
            const values = this.energyForm.value.Rows.some((es: any, i: any) => es.consumption !== null);
            if (values) {
              return false;
            }
            else {
              return true;
            }
          }
        }
      }
    }
  }
  onUnitChange(index: number, table?: any[], form?: UntypedFormGroup) {
    // Handle case when form is not provided
    if (!form) {
      if (this.istoggleValueThird) {
        form = this.energyThirdForm;
        table = this.thirdTableDetails;
      } else if (this.istoggleValueFourth) {
        form = this.energyFourthForm;
        table = this.fourthTableDetails;
      } else {
        form = this.energyForm;
        table = this.tableDetails;
      }
    }
    
    try {
      const formArray = form.get('Rows') as UntypedFormArray;
      if (!formArray || index >= formArray.length) {
        console.error("Invalid form array or index out of bounds:", index);
        return;
      }
      
      const formGroup = formArray.at(index) as UntypedFormGroup;
      if (!formGroup) {
        console.error("Form group not found at index:", index);
        return;
      }
      
      const unitValue = formGroup.get('unit')?.value;
      const consumptionValue = formGroup.get('consumption')?.value;
      
      // Only proceed if we have a unit value
      if (unitValue) {
        // If we have consumption value, use it directly
        if (consumptionValue) {
          this.onSearchChange(consumptionValue, index, table, form);
        } 
      }
    } catch (error) {
      console.error("Error in onUnitChange:", error);
    }
  }
  showErrorMessage() {
    this.dialogRef = this.modalService.open(this.errorPopUP, { centered: true });
  }
  close() {
    this.dialogRef.close();
  }
  openViewMore(tem) {
    this.dialogRef = this.modalService.open(tem, { centered: true });
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  showTable(condition: boolean) {
    this.showTab = condition;
  }
  get rows() {
    return (this.energyForm.get('Rows') as UntypedFormArray);
  }

  resetField(fieldName) {
    this.rows.controls.forEach(group => group.get(fieldName).reset());
  }
  initiateTable1() {
    this.spinner.show();
    const tableDetails1 = this.tableQuestion1.options;
    this.tableDetails1 = this.FilterTableQueFromSea(tableDetails1);
    this.tableform1 = this.fb.group({
      Rows: this.fb.array(this.tableDetails1.map((control: any) => this.createTable1formcontrol(control)))
    });
    if (!this.questionResult.length) {
      this.patchQuesWithValues();
    }
    this.dataSource1 = new MatTableDataSource((this.tableform1.get('Rows') as UntypedFormArray).controls);
    this.spinner.hide();
  }
  triggerWaterTableCalculations() {
    if (this.energySecondForm && this.energySecondForm.get('Rows')) {
      const rows = this.energySecondForm.get('Rows') as UntypedFormArray;
      for (let i = 0; i < rows.length; i++) {
        this.onSearchChangeWaterTable(i, this.energySecondForm);
      }
    }
  }
  createTable1formcontrol(control: any) {
    const options = (this.tableQuestion1.language[this.language] ?
      this.tableQuestion1.language[this.language].options : this.tableQuestion1.options)
      .find((option: any) => option.question_id === control.question_id);
    control.name = options.name;
    control.description = options.description;
    if (this.questionResult.length > 0) {
      // tslint:disable-next-line:max-line-length
      const result = (this.questionResult || []).filter(q => q.question === this.tableQuestion1.id && q.sequence === this.tableQuestion1.sequence && q.question_format === this.tableQuestion1.question_format);
      try {
        this.istoggleValue = JSON.parse(result[0].unit).toggle;
        this.fourthistoggleValue = JSON.parse(result[0].units).toggle;
        this.payingistoggleValue = JSON.parse(result[0].paying).toggle;
        this.changeYesOrNo(this.istoggleValue);
        this.fourthchangeYesOrNo(this.fourthistoggleValue);
      } catch (e) {
        this.istoggleValue = result[0].unit.toggle;
        this.fourthistoggleValue = result[0].units.toggle;
        this.payingistoggleValue = result[0].paying.toggle;
        this.changeYesOrNo(this.istoggleValue);
        this.fourthchangeYesOrNo(this.fourthistoggleValue);
        this.payingchangeYesOrNo(this.payingistoggleValue);
      }
      if (this.istoggleValue) {
        this.istoggle = true;
      }
      if (this.fourthistoggleValue) {
        this.fourthistoggle = true;
      }
      if (this.payingistoggleValue) {
        this.payingthistoggle = true;
      }
      let obj = [];
      try {
        obj = JSON.parse(result[0]?.answer);
      } catch (e) {
        obj = [];
      }
      const answer = (obj || []).filter(r => r.question_id === control.question_id);
      if (result.length > 0) {
        return this.fb.group({
          name: new UntypedFormControl(control?.name),
          unit: new UntypedFormControl(answer[0]?.unit),
          consumption: new UntypedFormControl(answer[0]?.consumption),
          question_id: new UntypedFormControl(control.question_id),
          prefilled: new UntypedFormControl(answer[0]?.prefilled),
          question_condition: new UntypedFormControl(answer[0]?.question_condition),
          co2elb: new UntypedFormControl(answer[0]?.co2elb),
          co2emb: new UntypedFormControl(answer[0]?.co2emb)
        });
      } else {
        return this.fb.group({
          name: new UntypedFormControl(control.name),
          unit: new UntypedFormControl(control.units[0]?.symbol),
          consumption: new UntypedFormControl(),
          question_id: new UntypedFormControl(control.question_id),
          prefilled: new UntypedFormControl(control.prefilled),
          question_condition: new UntypedFormControl(control?.question_condition),
          co2elb: new UntypedFormControl(),
          co2emb: new UntypedFormControl()
        });
      }
    } else {
      return this.fb.group({
        name: new UntypedFormControl(control.name),
        unit: new UntypedFormControl(control.units[0]?.symbol),
        consumption: new UntypedFormControl(),
        question_id: new UntypedFormControl(control.question_id),
        prefilled: new UntypedFormControl(control.prefilled),
        question_condition: new UntypedFormControl(control?.question_condition),
        co2elb: new UntypedFormControl(),
        co2emb: new UntypedFormControl()
      });
    }
  }
  getUnitBySingleOrMultipe(index: any) {
    return this.tableDetails1[index].unitdropDownType?.id === 1 ? true : false;
  }
  getUnitIsPer(index: any) {
    return this.thirdTableDetails[index].units?.[0]?.id === 1 ? true : false;
  }
  getUnitsFromIndex(index: any) {
    return this.tableDetails1[index]?.units[0]?.symbol;
  }
  getUnitList(index: number, table) {
    return (Array.isArray(this[table][index]?.units) && this[table][index]?.units.length > 0) ? this[table][index]?.units : weightUnits;
  }
  getOptionsList(i, table) {

  }
  changeUnitForTable1(index: number) {
    // const myForm = this.getTable1Form(index);
    this.emissionCalcVarTable1(index);
  }
  setValidForTableOptions(obj: any) {
    const validate = obj.condition;
    const formQuestionsControl = obj.control;
    this.tableDetails1.map((detail: any, i: number) => {
      const forms = this.getTable1Form(i);
      if (detail?.questionIdOfForm?.questionId === formQuestionsControl.question_id) {
        if (validate) {
          forms.controls.consumption.setValidators([Validators.required]);
          forms.controls.consumption.updateValueAndValidity();
        } else {
          forms.controls.consumption.clearValidators();
          forms.controls.consumption.updateValueAndValidity();
        }
      }
    });
  }
  getTable1Form(index) {
    return ((this.tableform1.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup);
  }
  getTable1FormControls(index) {
    return ((this.tableform1.get('Rows') as UntypedFormArray).at(index) as UntypedFormGroup).controls;
  }
  // this method for filtering a table options question or formquestions with condtion of which answer is answered in the SEA
  FilterTableQueFromSea(collQues: any): any {
    if (!this.questionsFromSea) {
      return collQues;
    }
    const questions = this.questionsFromSea.questions;
    for (const question of questions) {
      if (question.question.questionId === 'EM-GJR') {
        if (question.answer) {
          const answer = question?.answer?.name;
          const newQuestions = [];
          for (const formQue of collQues) {
            if (formQue.question_condition === '' || formQue.question_condition === answer) {
              newQuestions.push(formQue);
            }
          }
          return [...newQuestions];
        } else {
          return collQues;
        }
      }
    }
    return collQues;
  }
  emissionCalcVarTable1(i, value?: any, form?: UntypedFormGroup, table?: any[]) {
    const question_id = ['co_eg_006', 'co_eg_007', 'co_eg_008'];
    const currentRow = table[i];
    
    const emissionid = currentRow?.emissionType?.id;
    const emissionValue = (this.emissionService.carbonEmissionFactor || []).find((e: any) => e.emission_type === emissionid);
    const myForm = ((form.get('Rows') as UntypedFormArray).at(i) as UntypedFormGroup);

    let consumption: any = value;
    if (question_id.includes(currentRow.question_id) && this.firstTableForm?.form?.value['65'] === 'sodexo') {
      const data = [];
      table.map((de: any, i2) => {
        if (question_id.includes(de.question_id)) {
          const myinForm = ((form.get('Rows') as UntypedFormArray).at(i2) as UntypedFormGroup);
          data.push(myinForm.value.consumption);
        }
      });
      const condition = data.some((de1: any) => typeof de1 === 'number' ? true : false);
      table.map((detail: any, i1: number) => {
        const forms = this.getTable1Form(i1);
        if (question_id.includes(detail.question_id)) {
          if (condition) {
            forms.controls?.consumption.clearValidators();
            forms.controls?.consumption.updateValueAndValidity();
          } else {
            forms.controls.consumption.setValidators([Validators.required]);
            forms.controls.consumption.updateValueAndValidity();
          }
        }
      });
    }
    if (this.getUnitIsPer(i)) {
      myForm.controls.consumption.clearValidators();
      myForm.controls.consumption.updateValueAndValidity();
      if (consumption > 100) {
        myForm.controls.consumption.setValidators([percentageValidator(consumption, 2)]);
        myForm.controls.consumption.updateValueAndValidity();
      }
    }
    if (currentRow.unitdropDownType.id === 2) {
      const units = currentRow.units;
      units.map((unit: any) => {
        if (unit.symbol === myForm.value.unit) {
          if (question_id.includes(currentRow.question_id)) {
            if (unit.id === 3) {
              consumption *= 0.52971;
            } else if (unit.id === 4) {
              consumption *= 1.95993;
            } else if (unit.id === 5) {
              consumption *= 1;
            }
          }
        }
      });
    }
    let cal;
    if (currentRow.isDisabled) {
      cal = ((consumption / 100) * (emissionValue?.value || 0));
    } else {
      cal = (consumption * (emissionValue?.value || 0)) / 1000;
    }
      
    // if (cal) {
    myForm.patchValue({
      co2elb: cal.toFixed(3)
    });
    // }
    if (currentRow.isTonCalculation) {
      const isTonCalArr = [];
      currentRow.logic.map((logi: any) => {
        if (logi.field.id === 1) {
          isTonCalArr.push(logi);
        }
      });
      if (isTonCalArr.length) {
        this.calculateMarketBased(isTonCalArr);
      }
    }
    this.dataSource1 = new MatTableDataSource((this.energyThirdForm.get('Rows') as UntypedFormArray).controls);
  }
  emissionCalcVarTable4(i, value?: any, form?: UntypedFormGroup, table?: any[]){
    const myForm = ((form.get('Rows') as UntypedFormArray).at(i) as UntypedFormGroup);
    const fuelType = myForm.value.fuelType;
      const emissionTypeForFule = this.allEmissionTypes.find(e => e.name === fuelType);
      const emissionValueForFuel = (this.emissionService.carbonEmissionFactor || []).find(e => e.emission_type === emissionTypeForFule.id);
    
      if(!myForm?.value?.consumption){
        myForm.get('consumption').setValue(value);
      } 
      const val = myForm.get('consumption')?.value || value || 0;

       let unitValue = val;
        const unit = this.selectedUnit;
        const currentRow = table[i];
        if (currentRow?.unitdropDownType?.id !== 1) {
          let obj: any;
          if (currentRow.isUserCanChangeUnit) {
            obj = (currentRow?.units || []).find((unit: any) => myForm.value.unit == unit?.value);
            unitValue *= obj?.conversionValue || 1;
          } else {
            obj = (currentRow?.units || []).find((unit: any) => this.unit == unit?.value);
            unitValue *= obj?.conversionValue || 1;
          }
        }
        
      let cal = (unitValue * (emissionValueForFuel?.value || 0)) / 1000;
      myForm.patchValue({
        co2elb: cal.toFixed(3)
      });
  }     
  onFuelChange(rowIndex, selectedValue, form, table) {
    const currentRow = (this.energyFourthForm.get('Rows')  as UntypedFormArray).at(rowIndex);
    const targetForm = ((this.energyFourthForm.get('Rows') as UntypedFormArray).at(rowIndex) as UntypedFormGroup);

    targetForm.get('fuelType').setValue(
      selectedValue
    );

    this.emissionCalcVarTable4(rowIndex, targetForm.get('consumption')?.value || 0, form, table);
  }
  calculateMarketBased(isTonCalArr: any) {
    const electricResidualMix = this.firstTableForm?.electricResidualMixEmission;
    isTonCalArr.map((res: any) => {
      const targetForm = (this.tableform1.get('Rows') as UntypedFormArray).at(this.indexReturnFromstring(res.target.id));
      const Fvalues = [];
      // const tco2elb
      if (res.from.length) {
        res.from.map((inAr: any) => {
          const innerFromIdIndex = this.indexReturnFromstring(inAr?.id);
          const fromform = (this.tableform1.get('Rows') as UntypedFormArray).at(innerFromIdIndex);
          Fvalues.push(fromform.value);
        });
      }
      const f1cons = parseFloat(Fvalues[0].consumption || 0);
      const f2 = parseFloat(Fvalues[1].co2elb || 0);
      const f3 = parseFloat(Fvalues[2].co2elb || 0);
      // tslint:disable-next-line:max-line-length
      // const cal = (f2 + f3 + ((f1cons - (Fvalues[1].consumption / 100) - (Fvalues[2].consumption / 100)) * electricResidualMix.value)) / 1000;
      const f1Cons = Fvalues[0]?.consumption;
      const addedTwoRenewable = (Fvalues[1]?.consumption || 0) + (Fvalues[2]?.consumption || 0);
      const innerFromIdIndex = this.indexReturnFromstring(Fvalues[1]?.question_id);
      const targetValidationFrom = (this.tableform1.get('Rows') as UntypedFormArray).at(innerFromIdIndex);
      if ((addedTwoRenewable > 100)) {
        targetValidationFrom.get('consumption').setValidators([percentageSumValidator(addedTwoRenewable, 2)]);
        targetValidationFrom.get('consumption').updateValueAndValidity();
      } else {
        targetValidationFrom.get('consumption').clearValidators();
        targetValidationFrom.get('consumption').updateValueAndValidity();
      }
      const remainingPercValues = (addedTwoRenewable * f1Cons) / 100;
      const subtractedValue = f1Cons - remainingPercValues;
      const cal = (subtractedValue) * electricResidualMix.value / 1000;
      targetForm.patchValue({
        co2emb: cal.toFixed(3)
      });
    });
  }
  changeYesOrNo(istoggleValue) {
    if (istoggleValue === '0' || istoggleValue === 0) {
      this.showTab = true;
    }
    if (istoggleValue === '1' || istoggleValue === 1) {
      this.showTab = false;
    }
  }
  changeYesOrNoThird(value: any) {
   if (value === '0' || value === 0) {
     this.showThirdTab = true;
   } else {
     this.showThirdTab = false;
   }
  }

  changeYesOrNoFourth(value: any) {
   if (value === '0' || value === 0) {
     this.showFourthTab = true;
   } else {
      this.showFourthTab = false;
    }
  }

  fourthchangeYesOrNo(istoggleValue) {
    if (istoggleValue === '0' || istoggleValue === 0) {
      this.showTabeFourth = true;
    }
    if (istoggleValue === '1' || istoggleValue === 1) {
      this.showTabeFourth = false;

    }
  }
  payingchangeYesOrNo(istoggleValue) {
    if (istoggleValue === '0' || istoggleValue === 0) {
      this.showTabePaying = true;
    }
    if (istoggleValue === '1' || istoggleValue === 1) {
      this.showTabePaying = false;

    }
  }
  patchQuesWithValues() {
    const i = 0;
    let answer: any;
    const questions = this.questionsFromSea.questions;
    for (const question of questions) {
      if (question.question.questionId === 'EM-TDZ') {
        answer = question.answer;
      }
    }
    if (!answer) {
      return;
    }
    if (isNumeric(answer.value)) {
      const form = (this.tableform1.get('Rows') as UntypedFormArray).at(i);
      form.patchValue({
        consumption: parseFloat(answer.value)
      });
    }
    this.emissionCalcVarTable1(i);
  }
  showPercentageErr() {
    this.dialogRef = this.modalService.open(this.errorPercentage, { centered: true });
  }
  indexReturnFromstring(questionId: string) {
    return this.tableDetails1.findIndex((details: any) => details.question_id === questionId);
  }
  toggleSidenav() {
    this.sidenavOpened = !this.sidenavOpened;
    this.site.setSidenavState(this.sidenavOpened);
  }
  calculateReportingTotal(): number {
    let total = 0;
    if (this.energySecondForm && this.energySecondForm.get('Rows')) {
      const rows = this.energySecondForm.get('Rows') as UntypedFormArray;
      rows.controls.forEach((row: any) => {
        const cerpValue = row.get('Cerp')?.value;
        if (cerpValue && !isNaN(cerpValue)) {
          total += parseFloat(cerpValue);
        }
      });
    }
    this.totalElectricityConsumptionReportingPeriod = total || 0;
    return total;
  }
  calculateLocalTotal(): string {    
    this.locationBasedFactor();
    if (this.energySecondForm && this.energySecondForm.get('Rows')) {
      const calLocationBased = (this.totalElectricityConsumptionReportingPeriod * (this.locationBasedFactorForTotalCalc || 0)) / 1000;
      return calLocationBased.toFixed(3);
    }
  }
  calculateMarketTotal(): string {
    if (this.energySecondForm && this.energySecondForm.get('Rows')) {
      const calMarketBased = (this.percent * (this.energyRenewable + this.energyNuclear) + ((this.totalElectricityConsumptionReportingPeriod - this.percent) * this.energyResidualMix)) / 1000;
      return calMarketBased.toFixed(3);
    }
  }
  transformImageName(name: string): string {
    return name.replace(/\s+/g, '').toLowerCase();
  }
}

function isNumeric(str: string): boolean {
  if (typeof str !== 'string') { return false; }
  if (typeof str === 'number') { return true; }
  // tslint:disable-next-line:radix
  return !isNaN(parseInt(str)) || !isNaN(parseFloat(str));
}
