import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ListComponent } from './list/list.component';
import { LanguageResolve } from 'src/app/shared/resolves/language.resolve';


const routes: Routes = [
  {
    path: '',
    component: ListComponent,
    resolve: {language: LanguageResolve}
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CategoriesRoutingModule { }
