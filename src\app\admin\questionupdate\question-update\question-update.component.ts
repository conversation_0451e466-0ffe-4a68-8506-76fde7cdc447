import { HttpResponse } from '@angular/common/http';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { QuestionService } from 'src/app/shared/services/question.service';
import * as XLSX from 'xlsx';
@Component({
  selector: 'app-question-update',
  templateUrl: './question-update.component.html',
  styleUrls: ['./question-update.component.scss']
})
export class QuestionUpdateComponent implements OnInit {
  forms: any;
  questionValue: any;
  optionsArr: any[] = [];
  configs = {
    displayKey: 'name',
    search: true,
    placeholder: 'Choose services',
    searchPlaceholder: 'Search service'
  };
  answerValue: any;
  allowMultipleSelection = false;
  questionResults: any = [];
  responseData = {
    siteInsertCount: 0,
    siteErrorCount: 0,
    sitesExist: 0,
    siteNotFound: 0
  };
  dialogRef: any;
  filePath = './assets/files/questionupdate.xlsx';
  @ViewChild('sampleDailog') sampleDailog: TemplateRef<any>;

  constructor(
    private fb: UntypedFormBuilder,
    private questionService: QuestionService,
    private toaster: ToastrService,
    private spinner: NgxSpinnerService,
    private qtrlt: QuestionService,
    private dialog: MatDialog,
  ) { }

  ngOnInit(): void {
    this.createForm();
  }

  createForm() {
    this.forms = this.fb.group({
      questionId: ['', Validators.required],
      type: [''],
      options: [''],
      answer: [''],
      siteId: ['']
    });
  }

  getQuestionDetails() {
    this.spinner.show();
    this.questionService.getQuestionDetails(`questionId=${this.forms.value.questionId}`).subscribe((res: HttpResponse<any>) => {
      this.spinner.hide();
      this.questionValue = res.body[0];
      this.optionsArr = [];
      this.patchForm();
    });
  }

  patchForm() {
    this.responseData = {
      siteInsertCount: 0,
      siteErrorCount: 0,
      sitesExist: 0,
      siteNotFound: 0
    };
    this.forms.get('options').reset();
    this.forms.get('answer').reset();
    this.forms.get('siteId').reset();
    if (!(!!this.questionValue?.questionType)) {
      this.toaster.warning('Please Give Valid QuestionId');
      return;
    }
    this.forms.patchValue({
      type: this.questionValue.questionType
    });
    if (this.questionValue.questionType === 'checkbox') {
      this.allowMultipleSelection = true;
    } else {
      this.allowMultipleSelection = false;
    }
    this.optionsArr = [...this.questionValue.options];
  }

  changeDropdown(event) {
    this.answerValue = event.value;
    let name = '';
    if (this.answerValue.length > 1) {
      this.answerValue.forEach(element => {
        name = name === '' ? name + element.name : name + '::' + element.name;
      });
    } else {
      name = Array.isArray(this.answerValue) ? this.answerValue[0].name : this.answerValue.name;
    }
    this.forms.patchValue({
      answer: name
    });
  }

  updateQuestions() {
    const payload = {
      answer: this.forms.value.type === 'checkbox' ? this.forms.value.answer.split('::') : this.forms.value.answer,
      questionId: this.forms.value.questionId,
      sites: this.forms.value.siteId
    };
    if (payload.answer && payload.questionId && payload.sites) {
      this.spinner.show();
      this.questionService.updateQuestionQuestionResult(payload).subscribe((res: any) => {
        this.spinner.hide();
        this.responseData = res.data;
        this.toaster.success('answer added successfully');
      });
    } else {
      this.toaster.warning('Please enter the all fields');
    }
  }
  handleFileSelect(event, condition) {
    const files = event.target.files;
    this.questionResults = [];
    if (!files.length) {
      return 0;
    }
    const file = files[0];
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = (e: any) => {
      const data = new Uint8Array(e.target.result);
      const arr = new Array();
      for (let i = 0; i !== data.length; ++i) { arr[i] = String.fromCharCode(data[i]); }
      const bstr = arr.join('');
      const workbook = XLSX.read(bstr, { type: 'binary' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const objData = XLSX.utils.sheet_to_json(worksheet, { raw: true });
      const filesKeys = Object.keys(objData[0]);
      const result = objData.map((obj) => {
        return filesKeys.reduce((r, key) => {
          const k = key;
          r[k] = obj[key];
          // }
          return r;
        }, {});
      });
      result.map((item: any) => {
        const questionItems = Object.keys(item);
        const questions = [];
        const siteDetails: any = {};
        questionItems.map(obj => {
          const questionsAnswer = {};
          if (obj && obj.includes('-')) {
            if (item[obj]) {
              questionsAnswer[obj] = item[obj];
            }
            questions.push(questionsAnswer);
          } else {
            siteDetails[obj] = item[obj];
          }
        });

        this.questionResults.push({ Questions: questions, siteDetails });
      });
      if (this.questionResults.length) {
        this.importQuestionsResults(this.questionResults);
      }
    };
  }
  importQuestionsResults(payload: any) {
    this.spinner.show();
    this.qtrlt.updateQuestionResult(payload).subscribe((res: any) => {
      this.closesampleDailog();
      this.spinner.hide();
      this.toaster.success(res.message);
    }, (error) => {
      this.toaster.warning('Somthing went wront');
    });
  }
  openPoPupForSampleExportFile() {
    this.spinner.hide();
    this.dialogRef = this.dialog.open(this.sampleDailog, {
      disableClose: true,
      hasBackdrop: true,
      height: '30%',
      width: '30%'
    });
  }
  closesampleDailog() {
    this.dialogRef.close();
  }
}
