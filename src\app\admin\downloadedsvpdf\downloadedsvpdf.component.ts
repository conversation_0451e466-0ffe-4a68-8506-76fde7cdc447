import { Component, OnInit } from '@angular/core';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { ExportToCsv } from 'export-to-csv';
import { NgxSpinnerService } from 'ngx-spinner';
import { LanguageTranslateService } from 'src/app/carbon/services/language-translate.service';
import { CsvfilestatusService } from 'src/app/shared/services/csvfilestatus.service';

@Component({
  selector: 'app-downloadedsvpdf',
  templateUrl: './downloadedsvpdf.component.html',
  styleUrls: ['./downloadedsvpdf.component.scss']
})
export class DownloadedsvpdfComponent implements OnInit {
  searchField: any;
  records: any = [];
  totalCount = 0;
  ColumnMode = ColumnMode;
  limit = 10;
  offset = 0;
  options = {
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalSeparator: '.',
    showLabels: true,
    useTextFile: false,
    useBom: true,
    useKeysAsHeaders: true,
    filename: 'Downloaded SocialValue PPT'
  };
  sortColumn: string = '';
  sortOrder: string = '';

  constructor(
    private status: CsvfilestatusService,
    private spinner: NgxSpinnerService,
    private lts: LanguageTranslateService,
  ) { }
  readonly headerHeight = 50;
  readonly footerHeight = 50;
  readonly rowHeight = 50;
  readonly pageLimit = 25;
  ngOnInit(): void {
    this.getAllRecords();
  }
  search(event) {
    this.onsearch();
  }
  getAllRecords() {
    this.spinner.show();
    this.status.getSeaStatus().subscribe((res: any) => {
      this.spinner.hide();
      this.records = res.filter((val: any) => val.fileType === 'svpdf');
      this.totalCount = this.records.length;
    });
  }
  CheckRecords(event, row) { }
  paginate(event) {
    this.limit = event.limit;
    this.offset = event.offset;

      if (!this.searchField) {
      this.status.pagnationSeaStuatus(this.offset * this.limit, 'svpdf', this.sortOrder).subscribe((res: any) => {
        this.records = res;
      });
    } else {
      this.status.getAllSeastatus(this.searchField, this.limit, this.offset * this.limit, 'svpdf', this.sortOrder).subscribe((res: any) => {
        this.records = res.rows;
      });
    }
  }
  sort(event: any) {
    this.searchField = '';
    const sort = event.sorts[0];
    const sortColumn = sort.prop;
    const sortOrder = sort.dir;
    this.sortColumn = sort.prop;
  this.sortOrder = sort.dir;
    this.offset = 0;
    this.spinner.show();
    this.status.getAllSeastatus('', this.limit, this.offset * this.limit, 'svpdf', sortOrder).subscribe((res: any) => {
      this.records = [...res.rows];
      this.totalCount = res.totalCount || this.records.length;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    });
  }
  download(row) {        
    this.lts.getFile({blobName: row.blobname,containerName: row.fileType, fileUrl: row.url}).subscribe((res:any)=>{
      window.open(res.url, '_blank');
    } , (e => {
      console.log('error', e);
    }))
    // window.location.href = row.csvurl;
    // window.open(row.url, '_blank');
  }
  onsearch() {
    // this.status.getAllSeastatus(this.searchField, this.limit).subscribe((res: any) => {
    //   this.records = res.rows;
    // });
    const searchValue = (this.searchField || '').toLowerCase();
    if (!searchValue) {
      this.getAllRecords();
      return;
    }
    this.status.getSeaStatus().subscribe((res: any) => {
      const allRecords = res.filter((val: any) => val.fileType === 'svpdf');
      this.records = allRecords.filter((row: any) =>
        (row.siteId && row.siteId.toString().toLowerCase().includes(searchValue))
      );
      this.totalCount = this.records.length;
    });
  }
  delete(row: any) {
    this.status.deletestatus(row.blobname).subscribe((res: any) => {
      this.getAllRecords();
    });
  }
  Export() {
    this.spinner.show();
    const data = [];

    this.records.map((obj: any) => {
      const payload = {
        Site: obj.site,
        SiteId: obj.siteId,
        Date: obj.Date ,
        DownloadedBy: obj.downloadedBy ,
        URL: obj.url , 
        Type: "svpdf"
      };

      data.push(payload);
    });

    const csvExporter = new ExportToCsv(this.options);
    csvExporter.generateCsv(data);
    this.spinner.hide();
  }
}
