import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { InitiativeRoutingModule } from './initiative-routing.module';
import { ListComponent } from './list/list.component';
import { AddComponent } from './add/add.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { SelectDropDownModule } from 'ngx-select-dropdown';
import { MaterialModule } from 'src/app/shared/material.module';
import {MatSelectModule} from '@angular/material/select';
import {MatInputModule} from '@angular/material/input';
import {MatFormFieldModule} from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { GoogleMapsModule } from '@angular/google-maps';


@NgModule({
  declarations: [
    ListComponent,
    AddComponent
  ],
  imports: [
    CommonModule,
    InitiativeRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    NgbModule,
    NgxDatatableModule,
    SelectDropDownModule,
    MaterialModule,
    MatFormFieldModule, 
    MatInputModule, 
    MatSelectModule,
    TranslateModule,
    GoogleMapsModule
  ],
})
export class InitiativeModule { }
