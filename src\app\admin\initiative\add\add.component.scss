.file-name {
    width: 100px; /* Set this to match the width of the image */
    white-space: nowrap; /* Prevents text from wrapping to a new line */
    overflow: hidden; /* Hides overflow text */
    text-overflow: ellipsis; /* Displays ellipsis (...) for truncated text */
  }

.attach-img{
    cursor: pointer;
    justify-content: center;
    align-items: center;
    display: flex;
    width: 25px;
    height: 25px;
}

.img-url{
    width: 75px;
    object-fit: cover;
    border: 1px solid;
    height: 49px;
    border-radius: 10px;
}

#saveBtn{
    opacity: 1;
    cursor: pointer;
}

#saveBtn:disabled{
    opacity: 0.3;
    cursor: not-allowed;
}

::ng-deep .mat-mdc-dialog-surface{
    width: 1026px;
    height: 730px;
  }

::ng-deep .mat-mdc-dialog-content{
    min-height: 600px;
}
@media (min-width: 2000px) and (max-width: 2499px) {
    .zoom-content {
      zoom: 1.4 !important;
    }
}
@media (min-width: 2500px) and (max-width: 2999px) {
    .zoom-content {
      zoom: 1.6 !important;
    }
}
@media (min-width: 3000px) and (max-width: 3499px) {
    .zoom-content {
      zoom: 1.7 !important;
    }
}
@media (min-width: 3500px) {
    ::ng-deep .mat-mdc-dialog-surface{
        width: 2500px;
        height: 1400px;
      }
}
