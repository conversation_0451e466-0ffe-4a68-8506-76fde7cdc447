<div class="zoom-content">
    <div class="px-4 border-bottom">
        <h2 style="color: #2a295c;" mat-dialog-title>{{editInitiative ? 'Edit' : 'Add'}} Initiative</h2>
    </div>
    <mat-dialog-content>
        <div class="card-body mt-0">
            <form [formGroup]="initiativeForm" class="formwidth">
                <div class="row col-md-12 pt-2">
                    <div *ngIf="!isClickedFromHomepage" class="col-md-4">
                        <label  style="color: #2a295c;font-weight: 500;" for="initiativeId">{{'kInitiativeId' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="initiativeId" formControlName="initiativeId"
                            placeholder="Enter Initiative Id">
                    </div>
                    <div class="col-md-4">
                        <label  style="color: #2a295c;font-weight: 500;" for="initiativeName">{{'kInitiativeName' | translate}}</label>
                        <span class="text-danger">*</span>
                        <input style="color: black;" type="text" class="form-control" id="initiativeName" formControlName="initiativeName"
                            placeholder="Enter Initiative Name">
                        <div *ngIf="initiativeForm.get('initiativeName').invalid && initiativeForm.get('initiativeName').touched">
                            <small class="text-danger">Initiative Name is required.</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label  style="color: #2a295c;font-weight: 500;" for="Country">{{'kInitiativeCountry' | translate}}</label>
                        <ngx-select-dropdown [multiple]="true" formControlName="Country" [config]="initiativeconfig"
                            [options]="countries">
                        </ngx-select-dropdown>
                    </div>
        
                <!-- </div>
                <div class="row col-md-12 pt-2"> -->
                    <div class="col-md-4">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kKeyContact' | translate}}</label>
                        <span class="text-danger">*</span>
                        <input style="color: black;" type="email" class="form-control" id="Phone" formControlName="Phone"
                            placeholder="Enter Key Contact" required >
                        <div *ngIf="initiativeForm.get('Phone').invalid && initiativeForm.get('Phone').touched">
                            <small class="text-danger">Key Contact is required.</small>
                        </div>
                    </div>
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kInitialInvestment' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="Investment" formControlName="Investment"
                            placeholder="Enter Initial Investment">
                    </div>
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="imageUrl">{{'kOperatingExpense' | translate}}</label>
                        <span class="text-danger">*</span>
                        <ngx-select-dropdown [multiple]="false" formControlName="opex" [config]="opexconfig"
                            [options]="opexOptions">
                        </ngx-select-dropdown>
                        <div *ngIf="initiativeForm.get('opex').invalid && initiativeForm.get('opex').touched">
                            <small class="text-danger">Operating Expense is required.</small>
                        </div>
                    </div>
                <!-- </div>
                <div class="row col-md-12 pt-2"> -->
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kSaving' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="Saving" formControlName="Saving" placeholder="Enter Saving">
                    </div>
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kCurrency' | translate}}</label>
                        <span class="text-danger">*</span>
                        <ngx-select-dropdown [multiple]="false" formControlName="currency" [config]="currencyconfig"
                        [options]="currencys">
                        </ngx-select-dropdown>
                        <div *ngIf="initiativeForm.get('currency').invalid && initiativeForm.get('currency').touched">
                            <small class="text-danger">Currency is required.</small>
                        </div>
                    </div>
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kSavingPeriod' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="SavingPeriod" formControlName="SavingPeriod"
                            placeholder="Enter Saving Period">
                    </div>
                <!-- </div>
                <div class="row col-md-12 pt-2"> -->
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="Category">{{'kCategory' | translate}}</label>
                        <span class="text-danger">*</span>
                        <input style="color: black;" type="text" class="form-control" id="Category" formControlName="Category" placeholder="Enter Category">
                        <div *ngIf="initiativeForm.get('Category').invalid && initiativeForm.get('Category').touched">
                            <small class="text-danger">Category is required.</small>
                        </div>
                    </div>
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="Benefits">{{'kBenefits' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="Benefits" formControlName="Benefits" placeholder="Enter Benefits">
                    </div>
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="ClientReferences">{{'kClientReferences' | translate}}</label>
                        <span class="text-danger">*</span>
                        <input style="color: black;" type="text" class="form-control" id="ClientReferences" formControlName="ClientReferences" placeholder="Enter ClientReferences">
                        <div *ngIf="initiativeForm.get('ClientReferences').invalid && initiativeForm.get('ClientReferences').touched">
                            <small class="text-danger">Client References is required.</small>
                        </div>
                    </div>
                <!-- </div>
                <div class="row col-md-12 pt-2"> -->
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kSuitability' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="Suitability" formControlName="Suitability" placeholder="Enter Suitability">
                    </div>
                    <div  *ngIf="!isClickedFromHomepage" class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="ROI">{{'kROI' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="ROI" formControlName="ROI" placeholder="Enter Saving">
                    </div>
                    <div  *ngIf="!isClickedFromHomepage" class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="Implementation">{{'kImplementation' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="Implementation" formControlName="Implementation" placeholder="Enter Implementation">
                    </div>
                <!-- </div>
                <div class="row col-md-12 pt-2"> -->
                    <div class="col-md-7 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="imageUrl">{{'kService' | translate}}</label>
                        <ngx-select-dropdown [multiple]="true" formControlName="service" [config]="serviceconfig"
                            [options]="services">
                        </ngx-select-dropdown>
                    </div>
                    <div class="col-md-5 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="imageUrl">{{'kServiceProvider' | translate}}</label>
                        <span class="text-danger">*</span>
                        <ngx-select-dropdown [multiple]="false" formControlName="serviceprovider" [config]="serviceProviderconfig"
                            [options]="serviceProviders">
                        </ngx-select-dropdown>
                        <div *ngIf="initiativeForm.get('serviceprovider').invalid && initiativeForm.get('serviceprovider').touched">
                            <small class="text-danger">Service Provider is required.</small>
                        </div>
                    </div>
                <!-- </div>
                <div class="row col-md-12 pt-2"> -->
                    <div class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="imageUrl">{{'kTopic' | translate}}</label>
                        <ngx-select-dropdown [multiple]="false" formControlName="topic" [config]="topicconfig"
                            [options]="Topics">
                        </ngx-select-dropdown>
                    </div>
                    <div  *ngIf="!isClickedFromHomepage" class="col-md-4 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kScore' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="Score" formControlName="Score" placeholder="Enter Score">
                    </div>
                    <!-- <div class="col-md-4">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">Address</label>
                        <input nbInput [options]='options' class="form-control " ngx-google-places-autocomplete autocomplete="off" placeholder="Enter the Address" 
                        id="testdsafkj" name="testdsafkj" fullWidth formControlName="Address" #placesRef="ngx-places" style="color: black;" (onAddressChange)="handleAddressChange($event, 'address')">
                    </div> -->
                    <div  *ngIf="!isClickedFromHomepage" class="col-md-4 pt-2">
                        <label for="segType" style="color: #2a295c; font-weight: 500;">{{'kAddress' | translate}}</label>    
                        <input type="text" class="form-control" id="addressInput" formControlName="Address" placeholder="Enter the Address" #addressInput />
                    </div>
                <!-- </div>
                <div class="row col-md-12 pt-2"> -->
                    <div  *ngIf="!isClickedFromHomepage" class="col-md-3 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kLatitude' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="Lattitute" formControlName="lattitute" readonly>
                    </div>
                    <div  *ngIf="!isClickedFromHomepage" class="col-md-3 pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kLongitude' | translate}}</label>
                        <input style="color: black;" type="text" class="form-control" id="Langitute" formControlName="langitute" readonly>
                    </div>
                    <div class="col-md-6 flex-column d-flex pt-2">
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kImpact' | translate}} <span class="text-danger">*</span></label>
                        <textarea id="w3review" name="w3review" rows="4" cols="40" formControlName="impact" ></textarea>
                        <div *ngIf="initiativeForm.get('impact').invalid && initiativeForm.get('impact').touched">
                            <small class="text-danger">Impact is required.</small>
                        </div>
                    </div>
                </div>
                <!-- <div class="row col-md-12 pt-2">
                   <div class="col-md-12 justify-content-end d-flex p-3">
                    <button mat-raised-button color="primary" (click)="addNewinitiativeurl()"><mat-icon class="m-0">add</mat-icon></button>
                   </div> 
                </div> -->
                <!-- Attach images -->
                <div class="row col-md-12 pt-2">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <label style="color: #2a295c; font-weight: 500;" for="file-upload">
                                {{'kAttachImages' | translate}}
                            </label>
                            <span class="text-danger"><sup>*</sup></span>
                            <mat-icon class="mb-1" placement="right"
                                ngbTooltip="Accepted formats: [.jpg, .png, .jpeg]. Maximum 5 images can be uploaded. Upload images smaller than 5 MB.">info
                            </mat-icon>
                            <label for="file-upload" style="cursor: pointer; margin-left: 18px;">
                                <img src="assets/images/attachment.svg" alt="attachment" class="text-start attach-img" />
                            </label>            
                        </div> 
                        <input id="file-upload" type="file" accept=".jpg, .jpeg, .png" (change)="onFileSelected($event)" multiple style="display: none" />
                        <!-- <div *ngIf="fileSizeError" style="color: red;">{{ fileSizeError }}</div> -->
                        <div *ngIf="selectedFiles.length > 0">
                            <p>{{ selectedFiles.length }} file(s) selected</p>
                            <div class="d-flex flex-row">
                            <div class="preview-container position-relative g-5" *ngFor="let file of selectedFiles; let i = index">
                                <img width="100" class="img-url" [src]="file.preview" alt="Preview" />
                                <i class="fas fa-times position-absolute bg-white p-1" style="top: 0; right: 0; cursor: pointer;" (click)="removeFile(i)"></i>
                                <p class="mt-1 text-truncate file-name">{{ file.file.name }}</p>
                            </div>
                            </div>
                        </div>          
                    </div>
                </div>
                <small *ngIf="isFileSizeExceeded" class="text-danger">&nbsp; Uploaded image should be less than 5MB</small> 
                <small *ngIf="selectedFiles.length > 5" class="text-danger">&nbsp; Maximum 5 images can be uploaded</small> 
                <small *ngIf="selectedFiles.length == 0" class="text-danger">&nbsp;&nbsp; Atleast 1 image should be uploaded</small>    
                  
                <!-- <div class="row col-md-12 pt-2">
                    <div class="col-md-12">
                        <div formArrayName="initiativeurl" *ngFor="let initiative of initiativeurl().controls; let i = index">
                            <div [formGroupName]="i">
                              <label for="initiativeurl" style="color: #2a295c;">Initiative Url - {{i + 1}}</label>
                              <div class="d-flex">
                                  <input style="color: black;" type="text" class="form-control" id="initiativeurl" formControlName="imageUrl">
                                  <button mat-raised-button color="warn" (click)="removeinitiativeurl(i)"><mat-icon class="m-0">close</mat-icon></button>
                                </div>
                            </div>
                          </div>
                        </div>
                    </div> -->
                    <!-- <label for="initiativeurl" style="color: #2a295c;">Initiative Url</label>
                    <input style="color: black;" type="text" class="form-control" id="initiativeurl" formControlName="initiativeurl"> -->
                <div class="row col-md-12 pt-2 flex-column pl-4">
                    <!-- <div class="col-md-12"> -->
                        <label  style="color: #2a295c;font-weight: 500;" for="segType">{{'kDescription' | translate}} <span class="text-danger">*</span> </label>
                        <textarea id="w3review" name="w3review" rows="4" cols="80" formControlName="description" ></textarea>
                        <div *ngIf="initiativeForm.get('description').invalid && initiativeForm.get('description').touched">
                            <small class="text-danger">Description is required.</small>
                        </div>
                    <!-- </div> -->
                </div>
            </form>
        </div>
    </mat-dialog-content>
    <div class="border-top px-4">
        <div class="mt-2 mb-2"  align="end">
            <button type="button" (click)="closeInitiative()" class="btn btn-danger mr-2">Cancel</button>
            <button 
                type="button" 
                id="saveBtn" 
                (click)="addInitiative()" 
                class="btn btn-primary text-right" 
                [disabled]="initiativeForm.invalid || selectedFiles.length > 5 || selectedFiles.length === 0" 
                [title]="initiativeForm.get('currency').invalid ? '*Currency field is mandatory' : 
                        initiativeForm.get('opex').invalid ? '*Operational Expense field is mandatory' : 
                        initiativeForm.get('serviceprovider').invalid ? '*Service Provider field is mandatory' : 
                        initiativeForm.get('initiativeName').invalid ? '*Initiative Name field is mandatory' : 
                        initiativeForm.get('Category').invalid ? '*Category field is mandatory' : 
                        initiativeForm.get('ClientReferences').invalid ? '*Client References field is mandatory' : 
                        initiativeForm.get('impact').invalid ? '*Impact field is mandatory' : 
                        initiativeForm.get('description').invalid ? '*Description field is mandatory' : 
                        initiativeForm.get('Phone').invalid ? '*Key Contact field is mandatory' : ''">
                Save
            </button>
        
        </div>
    </div>
</div>