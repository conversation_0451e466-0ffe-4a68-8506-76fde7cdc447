<nav aria-label="breadcrumb">
    <ol class="breadcrumb bg-transparent py-0 px-3">
        <li class="breadcrumb-item">
            <a class="cursor-pointer" [routerLink]="['/admin/languages']" [queryParams]="breadCumsData">Languages</a>
        </li>
        <li class="breadcrumb-item active">{{edited ? "Edit": "Add"}}</li>
    </ol>
</nav>
<div class="container mt-4">
    <form [formGroup]="addForm" class="form form-horizontal" (ngSubmit)="create()">
        <div class="form-group row">
            <div class="col-md-12 form-group">
                <label for="languageName"> <sup>*</sup>Language Name</label>
                <input type="text" class="form-control" id="languageName" formControlName="name"
                    placeholder="Enter Language Name" [ngClass]="{ 'is-invalid': submitted && f.name.errors }">
                <span class="icon-position fas fa-globe"
                    [ngClass]="{ 'icon-error ': submitted && f.name.errors }"></span>
                <div *ngIf="submitted && f.name.errors" class="invalid-feedback">
                    <p *ngIf="f.name.errors.required"> Language Name is required</p>
                    <p *ngIf="f.name.errors.pattern"> Language Name is required</p>
                </div>
            </div>
            <div class="col-md-12 form-group">
                <label for="languageCode"> <sup>*</sup>Language Code</label>
                <input type="text" class="form-control" id="languageCode" formControlName="identifier"
                    placeholder="Enter Language Code" [ngClass]="{ 'is-invalid': submitted && f.identifier.errors }">
                <span class="icon-position fas fa-language"
                    [ngClass]="{ 'icon-error ': submitted && f.identifier.errors }"></span>
                <div *ngIf="submitted && f.identifier.errors" class="invalid-feedback">
                    <p *ngIf="f.identifier.errors.required">Language Code is required</p>
                    <p *ngIf="f.identifier.errors.pattern">Language Code is required</p>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-right pt-4">
                <button type="reset" (click)="listRecord()" class="btn btn-secondary mr-2">Cancel</button>
                <button type="submit" class="btn btn-primary text-right">{{edited ? "Update": "Add"}}</button>
            </div>
        </div>
    </form>
</div>