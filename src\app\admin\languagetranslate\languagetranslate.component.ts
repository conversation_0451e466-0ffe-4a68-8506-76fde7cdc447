import { Component, OnD<PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { LanguageTranslateService } from 'src/app/carbon/services/language-translate.service';
import { ConfirmationWindowComponent } from 'src/app/shared/components/confirmation-window/confirmation-window.component';
import { LanguageService } from 'src/app/shared/services/language.service';
import * as XLSX from 'xlsx';
import { Clipboard } from '@angular/cdk/clipboard';

@Component({
  selector: 'app-languagetranslate',
  templateUrl: './languagetranslate.component.html',
  styleUrls: ['./languagetranslate.component.scss']
})
export class LanguagetranslateComponent implements OnInit, OnDestroy {
  @ViewChild('kvalue')kvalueTem: TemplateRef<any>;
  subscription: Subscription = new Subscription();
  languages: any;
  searchField: any;
  language: any;
  dataSource = [];
  ColumnMode = ColumnMode;
  readonly headerHeight = 50;
  readonly footerHeight = 50;
  readonly rowHeight = 50;
  readonly pageLimit = 25;
  config = {
    search: true,
    height: '300px',
    placeholder: 'Select the Language',
    displayKey: 'name',
    searchPlaceholder: 'Search the Language',
  };
  offsetPaginate = 0;
  totalCount = 0;
  limit = 10;
  pageNumber = 1;
  dialogRef: any;
  languageKForm: UntypedFormGroup;
  submitted = false;
  addOrUpdate = true;
  rowData: any;
  searchDebounce: Subject<string> = new Subject();
  constructor(
    private dialog: MatDialog,
    private languageService: LanguageService,
    private ts: ToastrService,
    private spinner: NgxSpinnerService,
    private fb: UntypedFormBuilder,
    private lts: LanguageTranslateService,
    private bootDial: NgbModal,
    private clipboard: Clipboard
  ) { }

  ngOnInit(): void {
    this.createForm();
    this.setupSearchDeBouncer();
    this.getLanguage();
  }
  getLanguage() {
    this.spinner.show();
    const languages$ = this.languageService.getRecords().subscribe((data: any) => {
      this.languages = data;
      this.language = this.languages[0];
      this.spinner.hide();
      this.getList();
    }, error => {
      this.spinner.hide();
      throw error;
    });
    this.subscription.add(languages$);
  }
  setupSearchDeBouncer() {
    const search$ = this.searchDebounce.pipe(
      debounceTime(400),
      distinctUntilChanged(),
    ).subscribe((term: string) => {
      this.offsetPaginate = 0;
      this.getList();
    });
    this.subscription.add(search$);
  }

  search() {
    this.searchDebounce.next(this.searchField);
  }
  getList() {
    this.spinner.show();
    let search = '';
    // tslint:disable-next-line:max-line-length
    this.searchField?.length ? search = `&where=` + JSON.stringify({ or: [{Kvalue: { contains: this.searchField }}, {translatedContent: {contains: this.searchField}}], languageId : this.language?.id || 0}) :
    search = `&where=` + JSON.stringify({ languageId : this.language?.id || 0});
    this.lts.getRecords(this.limit, this.offsetPaginate, search).subscribe((res: any) => {
      this.dataSource = res.body;
      this.totalCount = parseInt(res.headers.get('content-count'), 10);
      this.spinner.hide();
    }, (err: any) => {
      this.spinner.hide();
      throw err;
    });
  }
  topicDialogOpen(type) {
    this.addOrUpdate = type;
    this.submitted = false;
    // tslint:disable-next-line:no-unused-expression
    type && this.languageKForm.reset();
    this.dialogRef = this.dialog.open(this.kvalueTem, {
      disableClose: true,
      hasBackdrop: true
    });
  }
  close() {
    this.dialogRef.close();
  }
  paginate(event) {
    this.offsetPaginate = event.offset * event.limit;
    this.getList();
  }
  createForm() {
    this.languageKForm = this.fb.group({
      Kvalue: ['', Validators.required],
      translatedContent: ['', Validators.required],
    });
  }
  createOrUpdate() {
    this.submitted = true;
    if (this.languageKForm.valid) {
      this.close();
      this.submitted = false;
      let payload = {...this.languageKForm.value, languageId: this.language.id};
      if (this.addOrUpdate) {
        this.lts.createLanguage(payload).subscribe((res: any) => {
          this.ts.success('created Successfully', 'Language convertion');
          this.languageKForm.reset();
          this.getList();
        }, (err: any) => {
          throw err;
        });
      } else {
        payload = {...this.languageKForm.value, languageId: this.rowData.languageId.id};
        this.lts.updateLanguage(this.rowData.id, payload).subscribe((res: any) => {
          this.ts.success('updated Successfully', 'Language convertion');
          this.rowData = undefined;
          this.languageKForm.reset();
          this.getList();
        }, (err: any) => {
          throw err;
        });
      }
    }
  }
  handleFileSelect(event: any) {
    this.spinner.show();
    const files = event.target.files;
    const file = files[0];
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = (e: any) => {
      const enc = new TextDecoder('utf-8');
      const data = new Uint8Array(e.target.result);
      // const arr = new Array();
      // for (let i = 0; i !== data.length; ++i) { arr[i] = String.fromCharCode(data[i]); }
      // const bstr = arr.join('');
      const bstr = enc.decode(data);
      const workbook = XLSX.read(bstr, { type: 'string' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const objData = XLSX.utils.sheet_to_json(worksheet, { raw: true });
      this.sendBulkDataToAPI(objData);
    };
  }
  sendBulkDataToAPI(objData: any[]) {
    const payload = [];
    for (const data of objData) {
      if (!data.Kvalue) {
        this.ts.error('file not corrected', 'Language Translated');
        this.spinner.hide();
        return;
      }
      payload.push({
        Kvalue: data.Kvalue,
        translatedContent: data.translatedContent,
        languageId: this.language.id
      });
    }
    this.lts.bulkUPLOAD(payload).subscribe((res: any) => {
      this.ts.success('Bulk uploaded successfully', 'Language Translated');
      this.onChange();
      this.spinner.hide();
    }, (err: any) => {
      this.ts.error('Bulk uploaded Failed', 'Language Translated');
    });
  }
  get f() {
    return this.languageKForm.controls;
  }
  onChange() {
    this.offsetPaginate = 0;
    this.getList();
  }
  // if check equal true edit / false delete
  action(row: any, check: boolean) {
    this.rowData = row;
    if (check) {
      this.languageKForm.patchValue({
        Kvalue: row.Kvalue,
        translatedContent: row.translatedContent
      });
      this.topicDialogOpen(false);
    } else {
      const activateModal = this.bootDial.open(ConfirmationWindowComponent);
      activateModal.componentInstance.title = 'Confirmation Delete Dialog';
      activateModal.componentInstance.content = 'Are you sure to soft delete this language translated';
      activateModal.componentInstance.updateState = 'yes';
      activateModal.result.then((res: any) => {
        this.delLang();
      }, (data) => {
        this.rowData = undefined;
      });
    }

  }
  delLang() {
    this.lts.deleteLanguage(this.rowData.id).subscribe((res: any) => {
      this.ts.success('Successfully Deleted', 'Language Id');
      this.rowData = undefined;
      this.offsetPaginate = 0;
      this.getList();
    });
  }
  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  export() {
    this.spinner.show();
    this.lts.getValueForExcel(this.language.id).subscribe((res: any) => {
      let csvData = [];
      const datas = res.body;
      if (datas.length) {
        csvData = datas.map((data: any) => {
          return {
          Kvalue: data.Kvalue,
          translatedContent: data.translatedContent,
          language: data.languageId.name};
        });
      } else {
        csvData.push({
          Kvalue: '',
          translatedContent: '',
          language: ''
        });
      }
      const workBook = XLSX.utils.book_new();
      const workSheet = XLSX.utils.json_to_sheet(csvData);
      XLSX.utils.book_append_sheet(workBook, workSheet, 'data');
      XLSX.writeFile(workBook, `fields_translate_file_${this.language.name}.csv`);
      this.spinner.hide();
    });
  }
  clickToCopy(content: any) {
    this.clipboard.copy(content);
  }
}
