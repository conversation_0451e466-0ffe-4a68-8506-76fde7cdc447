<nav aria-label="breadcrumb">
    <ol class="breadcrumb bg-transparent  py-0 px-3">
        <li class="breadcrumb-item">
            <a [routerLink]="['/admin/experts']" [queryParams]="breadCumsData">Experts</a>
        </li>
        <li class="breadcrumb-item active">{{ toggleName ? 'Edit':'Add'}}</li>
    </ol>
</nav>
<div class="container mt-4">
    <form [formGroup]="expertForm" class="form form-horizontal" (ngSubmit)="submit()">
        <div class="form-group row">
            <div class="col-md-6 form-group">
                <label for="expertName"> <sup>*</sup>First Name</label>
                <input type="text" class="form-control" id="expertName" formControlName="firstName"
                    placeholder="Enter Expert Name" [ngClass]="{ 'is-invalid': submitted && f.firstName.errors }">
                <span class="icon-position far fa-user"
                    [ngClass]="{ 'icon-error ': submitted && f.firstName.errors }"></span>
                <div *ngIf="submitted && f.firstName.errors" class="invalid-feedback">
                    <p *ngIf="f.firstName.errors.required">First Name is required</p>
                    <p *ngIf="f.firstName.errors.pattern">First Name is required</p>
                </div>
            </div>
            <div class="col-md-6 form-group">
                <label for="expertName"> <sup>*</sup>Last Name</label>
                <input type="text" class="form-control" id="expertName" formControlName="lastName"
                    placeholder="Enter Expert Name" [ngClass]="{ 'is-invalid': submitted && f.lastName.errors }">
                <span class="icon-position far fa-user"
                    [ngClass]="{ 'icon-error ': submitted && f.lastName.errors }"></span>
                <div *ngIf="submitted && f.lastName.errors" class="invalid-feedback">
                    <p *ngIf="f.lastName.errors.required">Last Name is required</p>
                    <p *ngIf="f.lastName.errors.pattern">Last Name is required</p>
                </div>
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-6 form-group">
                <label for="expertMail"> <sup>*</sup>Email</label>
                <input type="text" class="form-control" id="expertMail" formControlName="email"
                    placeholder="Enter Expert Email" [ngClass]="{ 'is-invalid': submitted && f.email.errors }" (change)="checkValidator($event.target.value,'expertUrl')">
                <span class="icon-position far fa-envelope"
                    [ngClass]="{ 'icon-error ': submitted && f.email.errors }"></span>
                <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
                    <p *ngIf="f.email.errors.required">Email is required</p>
                    <p *ngIf="f.email.errors.pattern">Email is required</p>
                </div>
            </div>
            <div class="col-md-6 form-group" id="id1">
                <label for="country"><sup>*</sup>Country</label>
                <input type="text" class="form-control" id="country" formControlName="country"
                    [ngbTypeahead]="searchExpertsCountry" [resultFormatter]="resultLookUp"
                    [inputFormatter]="inputLookUp" (focus)="focusCountry$.next($event.target.value)"
                    (click)="selectCountry$.next($event.target.value)" #instance="ngbTypeahead"
                    placeholder="Enter Expert's Country" [ngClass]="{ 'is-invalid': submitted && f.country?.errors }" [readonly]='isSubAdmin'/> 
                <span class="icon-position fa fa-flag-checkered"
                    [ngClass]="{ 'icon-error ': submitted && f.country.errors }"></span>
                <div *ngIf="submitted && f.country.errors" class="invalid-feedback">
                    <div *ngIf="f.country.errors.required"> Country is required</div>
                    <div *ngIf="f.country.errors.pattern">Country is required</div>
                </div>
            </div>
        </div>

        <div class="row form-group">
            <div class="col-md-6 form-group">
                <label for="expertType"><sup>*</sup>Type</label>
                <select (change)="onTypeChange($event.target.value)" class="form-control" id="expertType"
                    formControlName="type" [ngClass]="{ 'is-invalid': submitted && f.type?.errors }">
                    <option value="" disabled>Choose a type...</option>
                    <option *ngFor="let type of expertsType;trackBy: trackByFn">
                        {{type}}</option>
                </select>
                <div *ngIf="submitted && f.type.errors" class="invalid-feedback">
                    <div *ngIf="f.type.errors.required"> Type is required</div>
                </div>
            </div>
            <div class="col-md-6 form-group">
                <label for="segType">{{reqLabel ? '*Topic' : 'Topic'}}</label>
                <input #topicElement type="text" [attr.disabled]="true" class="form-control" id="segType"
                    formControlName="topic" [ngbTypeahead]="searchTopics" [resultFormatter]="resultTopics"
                    [inputFormatter]="inputTopics" (focus)="focusTopics$.next($event.target.value)"
                    (click)="selectTopics$.next($event.target.value)" #instanceTopics="ngbTypeahead"
                    placeholder="Enter Expert's Topic" [ngClass]="{ 'is-invalid': submitted && f.topic.errors }">
                <div *ngIf="submitted && f.topic.errors" class="invalid-feedback">
                    <div *ngIf="f.topic.errors.required">Topic is required</div>
                    <div *ngIf="f.topic.errors.pattern">Topic is required</div>
                </div>
            </div>
        </div>

        <div class="row form-group">
            <div class="col-md-6 form-group">
                <label for="expertNumber">Phone</label>
                <input type="number" class="form-control" id="expertNumber" formControlName="contactNumber"
                    placeholder="Enter Expert Number">
                <span class="icon-position fas fa-phone"></span>
            </div>
            <div class="col-md-6 form-group">
                <label for="expertDesign"> Designation</label>
                <input type="text" class="form-control" id="expertDesign" formControlName="designation"
                    placeholder="Enter Expert Designation"
                    [ngClass]="{ 'is-invalid': submitted && f.designation?.errors?.pattern }">
                <span class="icon-position fas fa-graduation-cap"
                    [ngClass]="{ 'icon-error ': submitted && f.designation?.errors?.pattern }"></span>
                <div *ngIf="f.designation?.errors?.pattern" class="invalid-feedback">
                    <p>Designation is required</p>
                </div>
            </div>
        </div>

        <div class="row form-group">
            <!-- <div class="col-md-6 form-group">
                <label for="practice">Best Practice</label>
                <div *ngIf="noPreview">
                    <img [src]="noPreview" class="text-center" width="200" height="158">
                    <div *ngIf="fileName" class="px-1 py-1  w-50">
                        <span class="text-break pb-1">{{fileName}}</span>
                    </div>
                </div>
                <input id="practiceFile" type="file" style="width: 0px;" accept=".pdf, .xls, .xlsx, .docx, .doc"
                    (change)="fileChange($event)" [ngClass]="{ 'is-invalid': submitted && f.bestPractices?.errors}"
                    class="invisible">
                <input type="hidden" style="width: 0px;" name="topicImage" formControlName="bestPractices">
                <div>
                    <label for="practiceFile" class="btn btn-primary w-200">Upload a file</label>
                </div>
            </div> -->
            <div class="col-md-6 form-group">
                <label for="profile-picture">Profile Picture</label>
                <div>
                    <img [src]="profilePreview" class="text-center" width="200">
                </div>
                <input id="profileImage" type="file" accept="image/*" (change)="fileChange($event)" accept="image/*"
                    class="invisible">
                <input type="hidden" name="profileImage" formControlName="imageUrl">
                <div>
                    <label for="profileImage" class="btn btn-primary w-200">Upload profile image</label>
                </div>
            </div>
            <div class="col-md-6 form-group" *ngIf='reqLabel'>
                <label for="expertUrl"> Expert URL</label>
                <input type="text" class="form-control" id="expertUrl" formControlName="expertUrl"
                    placeholder="Enter Expert URL" [ngClass]="{ 'is-invalid': submitted && f.expertUrl.errors }" (change)="checkValidator($event.target.value,'email')">
                <span class="icon-position fas fa-link"
                    [ngClass]="{ 'icon-error ': submitted && f.expertUrl.errors }"></span>
                <div *ngIf="submitted && f.expertUrl.errors" class="invalid-feedback">
                    <p *ngIf="f.expertUrl.errors.required">Expert URL is required</p>
                    <p *ngIf="f.expertUrl.errors.pattern">Expert URL is required</p>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-right pt-4">
                <button type="reset" (click)="close()" class="btn btn-secondary mr-2">Cancel</button>
                <button type="submit" class="btn btn-primary text-right">{{ toggleName ? 'Update':'Add'}}</button>
            </div>
        </div>
    </form>
</div>