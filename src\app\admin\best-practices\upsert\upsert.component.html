<nav aria-label="breadcrumb" class="d-flex justify-content-between pr-3">
    <div>
        <ol class="breadcrumb bg-transparent  py-0 px-3">
            <li class="breadcrumb-item">
                <a [routerLink]="['/admin/bestpractice']" [queryParams]="breadCumsData">Best Practices Guides</a>
            </li>
            <li class="breadcrumb-item active">{{ toggleName ? 'Edit':'Add'}}</li>
        </ol>
    </div>
    <!-- <div>
        <button type="" class="btn btn-primary text-right" (click)="openInitiative()">{{initiativeName ? 'Add': 'Edit'}}
            Initiative</button>
    </div> -->
</nav>
<div class="container mt-4">
    <form [formGroup]="practiceForm" class="form form-horizontal" (ngSubmit)="submit()">
        <div class="form-group row">
            <div class="col-md-6 form-group">
                <label for="bestPracticeId"><sup>*</sup>Best Practice key</label>
                <input type="text" class="form-control" id="bestPracticeId" formControlName="bestPracticeId"
                    placeholder="Enter the Best Practice Id"
                    [ngClass]="{ 'is-invalid': submitted && f.bestPracticeId.errors }" [readonly]='isSubAdmin'>

                <div *ngIf="submitted && f.bestPracticeId.errors" class="invalid-feedback">
                    <p *ngIf="f.score.bestPracticeId.required">Best Practice Id is required</p>
                </div>
            </div>
            <div class="col-md-6 form-group">
                <label for="expertName"> <sup>*</sup>Name</label>
                <input type="text" class="form-control" id="expertName" formControlName="name"
                    placeholder="Enter Expert Name" [ngClass]="{ 'is-invalid': submitted && f.name.errors }"
                    [readonly]='isSubAdmin'>

                <div *ngIf="submitted && f.name.errors" class="invalid-feedback">
                    <p *ngIf="f.name.errors.required">Name is required</p>
                    <p *ngIf="f.name.errors.pattern">Name is required</p>
                </div>
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-6 form-group">
                <label><sup>*</sup>Country</label>
                <ngx-select-dropdown [multiple]="false" formControlName="country" [config]="config"
                    [options]="countries" [ngClass]="{ 'is-invalid': submitted && f.country.errors }"
                    *ngIf="!isSubAdmin" (change)="countryBasedInitiative($event)">
                </ngx-select-dropdown>
                <input *ngIf="isSubAdmin" class="form-control" formControlName="country" [readonly]='isSubAdmin'>
                <span *ngIf="submitted && f.score.errors" class="invalid-feedback">
                    <p *ngIf="f.score.errors.required">Topic is required</p>
                </span>
            </div>
            <div class="col-md-6 form-group">
                <label for="segType">*Topic</label>
                <div class="dropdown" *ngIf='!isSubAdmin'>
                    <input class="form-control dropdown-toggle" id="dropdownMenuButton" data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false" formControlName="topic"
                        placeholder="Select the Topic" [ngClass]="{ 'is-invalid': submitted && f.topic.errors }">
                    <span *ngIf="submitted && f.score.errors" class="invalid-feedback">
                        <p *ngIf="f.score.errors.required">Topic is required</p>
                    </span>
                    <div class="dropdown-menu scrollable-menu" id="myDropdown">
                        <ng-container *ngFor="let lev0 of categoryList;trackBy: trackByFn">
                            <div class="px-2 pt-3">
                                <strong> {{lev0.name}} </strong>
                            </div>
                            <ng-container *ngFor="let lev1 of lev0.child;trackBy: trackByFn; let i = index">
                                <div class="px-2">
                                    <div class="dropdown-submenu px-2" *ngIf="lev1.isLeaf">
                                        <a class=" cursor-pointer" (click)="getQuestionL2(lev0,lev1)"> {{i+1}})
                                            {{lev1.name}}</a>
                                    </div>
                                    <div class="dropdown-submenu px-2" *ngIf="!lev1.isLeaf">
                                        <a>{{i+1}}) {{lev1.name}}</a>
                                    </div>
                                    <a class=" dropdown-item cursor-pointer text-dark"
                                        (click)="getQuestionL3(lev0, lev1, lev2)"
                                        *ngFor="let lev2 of lev1.child;trackBy: trackByFn">- {{lev2.name}}</a>
                                </div>
                            </ng-container>
                        </ng-container>
                    </div>
                </div>
                <div *ngIf='isSubAdmin'>
                    <input class="form-control" formControlName="topic" [readonly]='isSubAdmin'>
                </div>

            </div>
        </div>
        <div class="row form-group">

            <div class="col-md-6 form-group">
                <label for="imageUrl">Best Practice URL</label>
                <input type="text" class="form-control" id="imageUrl" formControlName="imageUrl"
                    placeholder="Provide the Best Practice URL"
                    [ngClass]="{ 'is-invalid': submitted && f.imageUrl.errors }">

                <div *ngIf="submitted && f.score.errors" class="invalid-feedback">
                    <p *ngIf="f.imageUrl.errors.required">Score is required</p>
                    <p *ngIf="f.imageUrl.errors.pattern">Score is required</p>
                </div>
            </div>
            <div class="col-md-6 form-group">
                <label for="segType">Score</label>
                <input type="number" class="form-control" id="expertName" formControlName="score"
                    placeholder="Enter the Score" [ngClass]="{ 'is-invalid': submitted && f.score.errors }"
                    [readonly]='isSubAdmin'>
                <div *ngIf="submitted && f.score.errors" class="invalid-feedback">
                    <p *ngIf="f.score.errors.required">Score is required</p>
                    <p *ngIf="f.score.errors.pattern">Score is required</p>
                </div>
            </div>
        </div>
        <div class="row form-group">
            <div class="col-md-6 form-group">
                <label for="imageUrl">Initiative Name</label>
                <ngx-select-dropdown [multiple]="true" formControlName="initiativeId" [config]="initiativeconfig"
                    [options]="totalNames">
                </ngx-select-dropdown>
            </div>
            <div class="col-md-6 form-group">
                <label  style="color: #2a295c;font-weight: 500;" for="segType">Description <span class="text-danger">*</span> </label>
                <textarea id="description" name="description" rows="4" cols="80" formControlName="description" ></textarea>
                <div *ngIf="practiceForm.get('description').invalid && practiceForm.get('description').touched">
                    <small class="text-danger">Description is required.</small>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-right pt-4">
                <button type="reset" (click)="close()" class="btn btn-secondary mr-2">Cancel</button>
                <button type="submit" class="btn btn-primary text-right">{{ toggleName ? 'Update':'Add'}}</button>
            </div>
        </div>
    </form>
    <div *ngIf="hideInitiativeTable">
        <div class="col-md-12 mt-4 d-flex justify-content-between">
            <div class="col-md-3 align-self-center">
                <h5 class="text-primary font-weight-bold m-0" style="font-size: 18px;">Initiative List</h5>
            </div>
        </div>
        <div class="col-md-12 mt-4">
            <ngx-datatable class="bootstrap cursor-pointer" [rows]="initiativeData" [columnMode]="ColumnMode.force"
                [headerHeight]="headerHeight" [footerHeight]="footerHeight" [rowHeight]="rowHeight" [limit]="limit"
                [sorts]="[{prop: 'name', dir: 'asc'}]" [offset]="offsetPaginate" [count]="count"
                [externalPaging]="false" style=" height: 100%;width: 100%;">

                <ngx-datatable-column [width]="30" [sortable]="false" [canAutoResize]="false" [draggable]="false"
                    [resizeable]="false" [headerCheckboxable]="true" [checkboxable]="true">
                </ngx-datatable-column>
                <ngx-datatable-column name="Initiative Id" prop="initiativeid" [minWidth] = "100">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column name="Initiative Name" prop="initiativename" [minWidth] = "130">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column name="Country" prop="country" [minWidth] = "100">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column name="Address" prop="address" [minWidth] = "100">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column name="Contact" prop="contact" [minWidth] = "100">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            'SiteManager'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column name="Investment" prop="investment" [minWidth] = "100">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column name="Saving" prop="saving" [minWidth] = "100">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <!-- <ngx-datatable-column name="Time" prop="time">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column> -->
                <ngx-datatable-column name="Period" prop="period" [minWidth] = "100">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column name="Score" prop="score" [minWidth] = "100">
                    <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value ||
                            '-'}}</span>
                    </ng-template>
                </ngx-datatable-column>
                <!-- <ngx-datatable-column name="Action" prop="Action" [maxWidth]="500">
                    <ng-template let-row="row" ngx-datatable-cell-template>
                        <mat-icon aria-hidden="false" (click)="openInitiative(true, row.id)">edit</mat-icon>
                    </ng-template>
                </ngx-datatable-column> -->
                <ngx-datatable-column name="IsDeleted" prop="status" headerClass="text-left" cellClass="text-left ps-1" [minWidth] = "100">
                    <ng-template let-value="value" let-row="row" ngx-datatable-cell-template>
                        <mat-icon aria-hidden="false" color="warn" (click)="deleteData(row)">delete_forever</mat-icon>
                    </ng-template>
                </ngx-datatable-column>
            </ngx-datatable>
        </div>
    </div>
</div>