import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Sort } from 'src/app/shared/services/user.service';
import { Router } from '@angular/router';
import { ColumnMode, DatatableComponent } from '@swimlane/ngx-datatable';
import { ImageuploadService } from 'src/app/shared/services/imageupload.service';


@Component({
  selector: 'app-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss']
})
export class ListComponent implements OnInit {
  records: any = [];
  columns: any = [];
  offsetPaginate = 0;
  limit = 10;
  offset = 0;
  totalCount: number;
  ColumnMode = ColumnMode;
  sortBy: Sort = { name: 'name', value: 'ASC' };
  @ViewChild('checkboxTemplate', { static: true }) checkboxTemplate: TemplateRef<any>;
  @ViewChild('createdTemplate', { static: true }) createdTemplate: TemplateRef<any>;
  @ViewChild('updatedTemplate', { static: true }) updatedTemplate: TemplateRef<any>;
  @ViewChild('headerTemplate', { static: true }) headerTemplate: TemplateRef<any>;
  @ViewChild('rowTemplate', { static: true }) rowTemplate: TemplateRef<any>;
  @ViewChild('dataTable', { static: true }) table: DatatableComponent;
  searchText: string;
  updateIDs = [];
  constructor(
    private router: Router,
    private im: ImageuploadService,
  ) { }

  ngOnInit(): void {
    this.columns = [
      // {
      //   cellTemplate: this.checkboxTemplate,
      //   name: '',
      //   prop: 'id',
      //   sortable: false,
      //   minWidth: 50,
      //   maxWidth: 50
      // },
      { prop: 'id', name: 'id', maxWidth: 100, sortable: false },
      { prop: 'fileName', name: 'fileName', maxWidth: 200, sortable: true },
      { prop: 'filePath', name: 'filePath', maxWidth: 400, sortable: false },
      { prop: 'fileType', name: 'fileType', maxWidth: 400, sortable: false },
      { prop: 'row', name: 'Position', maxWidth: 400, sortable: false },
      { prop: 'activate', name: 'isActive', maxWidth: 400, sortable: false },
      // {
      //   name: 'activate',
      //   prop: 'isActive',
      //   sortable: false
      // },
      {
        cellTemplate: this.createdTemplate,
        name: 'Created At',
        prop: 'createdAt',
        sortable: false
      },
      {
        cellTemplate: this.updatedTemplate,
        name: 'Updated At',
        prop: 'updatedAt',
        sortable: false
      },
      {
        cellTemplate: this.rowTemplate,
        headerTemplate: this.headerTemplate,
        name: 'Action',
        prop: 'id',
        sortable: false,
        maxWidth: 200,
        width: 160,
        minWidth: 160
      }

    ];
    this.getAllRecods();
  }
  paginate(event) {
    this.searchText = '';
    this.limit = event.limit;
    this.offset = this.limit * event.offset;
    this.offsetPaginate = event.offset;
    this.router.navigate(['admin/imageupload'], {
      queryParams: {
        limit: this.limit,
        offset: this.offset,
        sortName: this.sortBy.name,
        sortValue: this.sortBy.value
      }
    });
  }
  sort(event) {
    if (this.totalCount > 1) {
      this.sortBy.name = event.column.prop;
      this.sortBy.value = event.newValue;
      this.router.navigate(['admin/imageupload'], {
        queryParams: {
          limit: this.limit,
          offset: this.offset,
          sortName: this.sortBy.name,
          sortValue: this.sortBy.value
        }
      });
    } else {
      return false;
    }
  }

  getAllRecods() {
    this.im.getimagesRecords().subscribe((res: any) => {
      if (res) {
        this.records = res;
        this.totalCount = res.length;
      } else {
      }
    });
  }
  checkRecords(template, value) {
    if (template.checked) {
      this.updateIDs.push(value.id);
    } else {
      const index = this.updateIDs.findIndex(item => item.id === value.id);
      this.updateIDs.splice(index, 1);
    }
  }
  edit(id) {
    this.router.navigate([`admin/imageupload/edit/${id}`], {
      queryParams: {
        offset: this.offset,
      }
    });
  }
  topicDialogOpen() {
    this.router.navigate(['admin/imageupload/add']);
  }
}
