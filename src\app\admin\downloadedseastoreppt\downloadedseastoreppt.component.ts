import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ColumnMode } from '@swimlane/ngx-datatable';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { LanguageTranslateService } from 'src/app/carbon/services/language-translate.service';
import { CsvfilestatusService } from 'src/app/shared/services/csvfilestatus.service';
import { ExportToCsv } from 'export-to-csv';

@Component({
  selector: 'app-downloadedseastoreppt',
  templateUrl: './downloadedseastoreppt.component.html',
  styleUrls: ['./downloadedseastoreppt.component.scss']
})
export class DownloadedseastorepptComponent implements OnInit {
  @ViewChild('insertFile') insertFile: TemplateRef<any>;
  dialogRef: any;
  searchField: any;
  records: any = [];
  totalCount = 0;
  offset = 0;
  ColumnMode = ColumnMode;
  limit = 10;
  chooseFile: any;
  tableName = 'Choose File Type';
  volUnit= 1;
  filechoos = false;
  options = {
    fieldSeparator: ',',
    quoteStrings: '"',
    decimalSeparator: '.',
    showLabels: true,
    useTextFile: false,
    useBom: true,
    useKeysAsHeaders: true,
    filename: 'Downloaded SEA PPT'
  };
   sortColumn: string = '';
  sortOrder: string = '';

  constructor(
    private status: CsvfilestatusService,
    private spinner: NgxSpinnerService,
    private dialog: MatDialog,
    private toaster: ToastrService,
    private lts: LanguageTranslateService,
  ) { }
  readonly headerHeight = 50;
  readonly footerHeight = 50;
  readonly rowHeight = 50;
  readonly pageLimit = 25;
  ngOnInit(): void {
    this.getAllRecords();
  }
  search(event) {
    this.onsearch();
  }
  getAllRecords() {
    this.spinner.show();
    this.status.getSeaStatus().subscribe((res: any) => {
      this.spinner.hide();
      this.records = res.filter((val: any) => val.fileType === 'seaStorepptx'); 
      this.totalCount = this.records.length;
    });
  }
  CheckRecords(event, row) { }
  paginate(event) {
    this.limit = event.limit;
    this.offset = event.offset; 
    
    if (!this.searchField) {
      this.status.pagnationSeaStuatus(this.offset * this.limit, 'seaStorepptx', this.sortOrder).subscribe((res: any) => {
        this.records = res;
      });
    } else {
      this.status.getAllSeastatus(this.searchField, this.limit, this.offset * this.limit, 'seaStorepptx', this.sortOrder).subscribe((res: any) => {
        this.records = res.rows;
      });
    }
  }
  sort(event: any) {
    this.searchField = '';
    const sort = event.sorts[0];
    const sortColumn = sort.prop;
    const sortOrder = sort.dir;
    this.sortColumn = sort.prop;
  this.sortOrder = sort.dir;
    this.offset = 0;
    this.spinner.show();
    this.status.getAllSeastatus('', this.limit, this.offset * this.limit, 'seaStorepptx', sortOrder).subscribe((res: any) => {
      this.records = [...res.rows];
      this.totalCount = res.totalCount || this.records.length;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    });
  }
  download(row) {        
    this.lts.getFile({blobName: row.blobname,containerName: row.fileType, fileUrl: row.url}).subscribe((res:any)=>{
      window.open(res.url, '_blank');
    } , (e => {
      console.log('error', e);
    }))
    // window.location.href = row.csvurl;
    // window.open(row.url, '_blank');
  }
  onsearch() {
    // this.status.getAllSeastatus(this.searchField, 100, 0, 'sodexoppt').subscribe((res: any) => {
    //   this.records = res.rows;
    //   this.totalCount = res.rows.length;
    // });
     const searchValue = (this.searchField || '').toLowerCase();
    if (!searchValue) {
      this.getAllRecords();
      return;
    }
    this.status.getSeaStatus().subscribe((res: any) => {
      const allRecords = res.filter((val: any) => val.fileType === 'seaStorepptx');
      this.records = allRecords.filter((row: any) =>
        (row.siteId && row.siteId.toString().toLowerCase().includes(searchValue))
      );
      this.totalCount = this.records.length;
    });
  }
  delete(row: any) {
    this.status.deletestatus(row.blobname).subscribe((res: any) => {
      this.getAllRecords();
    });
  }
  openDialog() {
    this.tableName = 'Choose File Type';
    this.dialogRef = this.dialog.open(this.insertFile, {
      disableClose: true,
      hasBackdrop: true,
      width: '40%',
      panelClass: 'logFile',
      maxHeight: '72%'
    });
  }
  close() {
    // this.tableName = 'Choose File Type'
    this.dialogRef.close();
  }
  selectfile(e) {
    if (e == 1) {
        this.tableName = 'Choose File Type';
    }else if (e == 2) {   
      this.tableName = 'baseline';
    }else if (e == 3) {
      this.tableName = 'actual';
    }
  }
  fileupload(event: any) {
    this.chooseFile = event.target.files[0];
    this.filechoos = true;
  }
  submitFileUpload() {
    const formData: FormData = new FormData();
    formData.append('file', this.chooseFile);    
    if (this.tableName && this.tableName != 'Choose File Type' && this.filechoos) {
      this.close();
      this.spinner.show();
      this.status.fileUpload(formData, this.tableName).subscribe((res: any) => {
        this.toaster.success('File Inserted Successfully')
        this.spinner.hide();
      });
    } else if (!this.filechoos) {
      this.toaster.warning('Please choose a file')
    } else{
      this.toaster.warning('Please choose a file format')
    }
  }

    Export() {
      this.spinner.show();
      const data = [];

      this.records.map((obj: any) => {
        const payload = {
          Site: obj.site,
          SiteId: obj.siteId,
          Date: obj.Date ,
          DownloadedBy: obj.downloadedBy ,
          URL: obj.url , 
          Type: "SeaStore PPT"
        };

        data.push(payload);
      });

      const csvExporter = new ExportToCsv(this.options);
      csvExporter.generateCsv(data);
      this.spinner.hide();
    }
}
