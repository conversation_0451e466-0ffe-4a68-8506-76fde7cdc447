<div class="container-fluid">
    <div class="row justify-content-between">
        <div class="col-md-3 align-self-center">
            <h5 class="text-primary font-weight-bold m-0" style="font-size: 18px;">Image Upload</h5>
        </div>
        <div class="col-md-9 align-self-center">
            <div class="row justify-content-end">

                <div class="col-lg-5 search">
                    <input class="form-control py-2" type="text" name="searchField" id="searchField"
                        placeholder="Search by Image Name" style="margin-left:25px;">
                </div>
                <div class="col-md-3 d-flex justify-content-end">
                    <button placement="bottom" ngbTooltip="Add Image" class="btn btn-primary text-white form-control add-button" (click)="topicDialogOpen()"
                        type="submit"><i class="fas fa-plus d-inline fa-sm mr-1"></i><span class="custom-add">Add
                            Image</span></button>
                </div>
            </div>
        </div>
    </div>
    <div class="row form-group mt-4">
        <div class="col-md-12">
            <ngx-datatable class="bootstrap" [rows]="records" [columns]="columns" [loadingIndicator]="false"
            [limit]="limit" [offset]="offsetPaginate" [count]="totalCount" [externalPaging]="true"
            (page)="paginate($event)"
            [sorts]="[{prop: 'fileName', dir: 'asc'},]"
            [reorderable]="true" [externalSorting]="true" [columnMode]="ColumnMode.force" [headerHeight]="50"
            [footerHeight]="50" rowHeight="auto" (sort)="sort($event)" #dataTable>
        </ngx-datatable>
          <ng-template class="mx-2" #checkboxTemplate let-row="row" let-value="value">
                <form>
                    <input #checkBox name="check" type="checkbox" class="mx-2 myCheck" id={{value}}
                        (click)="checkRecords(checkBox, row)">
                </form>
            </ng-template>
            <ng-template #createdTemplate let-row="row" let-value="value">
                {{value | date :'longDate'}}
            </ng-template>
            <ng-template #updatedTemplate let-row="row" let-value="value">
                {{value | date :'longDate'}}
            </ng-template>
            <ng-template #headerTemplate let-column="column"> {{ column.name }}</ng-template>
            <ng-template #rowTemplate let-row="row" let-value="value">
                <i class="fas fa-edit cursor-pointer mr-4 ml-3" data-toggle="tooltip" data-placement="top" title="Edit"
                    (click)="edit(value)" aria-hidden="true"></i>
           </ng-template>
        </div>
    </div> 
</div>