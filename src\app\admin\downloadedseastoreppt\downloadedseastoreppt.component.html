<section class="my-3 p-3">
    <div class="container-fluid col-md-12">
        <div class="row">
            <div class="col-md-4 question-text">
                <h2 style="color:rgb(42,41,92);">File Download Status</h2>
            </div>
            <div class="col-md-8">
                <div class="row justify-content-end">
                    <div class="col-md-4 search">
                        <input type="text" class="form-control" placeholder="Search by SiteId" [(ngModel)]="searchField"
                            (input)="search($event)">
                    </div>
                    <div class="col-md-3">
                        <div>
                            <!-- <input type="file" (change)="fileupload($event)"> -->
                           <!-- <div class="btn btn-primary" (click)="openDialog()">Upload (Actual/BaseLine) File</div> -->
                           <button placement="bottom" ngbTooltip = "Export" class="btn btn-primary ml-3 changfont" (click)="Export()" type="submit"><i
                            class="fa fa-upload d-inline fa-sm mr-1"></i><span class="changfont">Export</span></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="">
    <div class="col-md-12" style="padding: 5px 30px; min-inline-size: 500px;">
        <div class="row form-group">
            <div class="col-md-12">
                <ngx-datatable class="bootstrap cursor-pointer" [rows]="records" [columnMode]="ColumnMode.force"
                    [headerHeight]="headerHeight" [footerHeight]="footerHeight" [rowHeight]="rowHeight" [limit]="limit"
                    (page)="paginate($event)" [sorts]="[{prop: 'name', dir: 'asc'}]" (sort)="sort($event)" [offset]="offset"
                    [count]="totalCount" [externalSorting]="true" [externalPaging]="true" style="block-size: 100%;">
                    <ngx-datatable-column name="Site" prop="site" [maxWidth]="350" [minWidth]="80">
                        <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value || '-'}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column name="Site Id" prop="siteId" [maxWidth]="250" [minWidth]="180">
                        <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value || '-'}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column name="Date" prop="Date"  [maxWidth]="300" [minWidth]="180">
                        <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value || '-'}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column name="DownloadedBy" prop="downloadedBy"  [maxWidth]="350" [minWidth]="250">
                        <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value || '-'}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column name="URL" prop="url"  [maxWidth]="350" [minWidth]="250">
                        <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value || '-'}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column name="Type" prop="fileType"  [maxWidth]="350" [minWidth]="150">
                        <ng-template let-value="value" ngx-datatable-cell-template> <span>{{value || '-'}}</span>
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column name="Action" prop="Action" [maxWidth]="350" [minWidth]="150">
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <span class="d-flex" style="padding: 4px;">
                                <i class="fas fa-download" (click)="download(row)"
                                    style="color:rgb(42,41,92);padding-inline-end: 10px;"></i>
                                <!-- <i class="fas fa-trash" (click)="delete(row)"></i> -->
                            </span></ng-template>
                    </ngx-datatable-column>
                </ngx-datatable>
            </div>
        </div>
    </div>
</section>





<ng-template #insertFile>
    <div class="card text-center h-100 p-2">
      <div class="card-header d-flex justify-content-between">
        <h2 class="changfont">Upload (Actual/BaseLine) File</h2>
        <div >
          <button  ngbTooltip="close" placement="bottom" type="button" class="close" aria-label="Close" (click)="close()">
            <span>
              <i class="fas fa-times text-muted fa-xs"></i>
            </span>
          </button>
        </div>
      </div>
      <div class="card-body d-flex justify-content-around" style="min-height: 130px;max-height: 200px;align-items: center;">
        <div>
            <select class="form-control" [(ngModel)]="volUnit" (change)="selectfile($event.target.value)">
                <option value="1" disabled>{{'---Choose File Type---'}}</option>
                <option value="2">{{'baseline'}}</option>
                <option value="3">{{'actual' }}</option>
              </select>
          <!-- <h3 class="changfont">Click to load social value data through SFTP</h3>
          <button class="btn btn-primary changfont"  >Load SFTP</button>
          <p class="mt-2 changfont" style="font-size: 12px;"><a href="assets/files/svProject.csv">Click</a> to load social value the sample CSV template file.</p> -->
        </div>
        <div>
            <input type="file" name="" id="" (change)="fileupload($event)">
        </div>
      </div>
      <div class="justify-content-end d-flex">
          <div class="btn btn-primary w-25" (click)="submitFileUpload()">submit</div>
      </div>
    </div>
  </ng-template>