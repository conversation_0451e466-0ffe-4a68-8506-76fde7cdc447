import { HttpResponse } from '@angular/common/http';
import { LanguageService } from './../../../shared/services/language.service';
import { Component, OnInit, ViewChild, TemplateRef, ChangeDetectorRef, OnDestroy } from '@angular/core';
import { Services } from 'src/app/shared/models/services';
import { Sort } from 'src/app/shared/services/user.service';
import { ColumnMode, DatatableComponent } from '@swimlane/ngx-datatable';
import { Router, ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { ConfirmationWindowComponent } from 'src/app/shared/components/confirmation-window/confirmation-window.component';
import { ConstantData } from 'src/app/shared/constants/constant';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { DataService } from 'src/app/shared/services/data.service';
import { Language } from 'src/app/shared/models/language.model';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss']
})
export class ListComponent implements OnInit, OnDestroy {
  value = false;
  records: Language[] = [];
  updateIDs = [];
  InActivateData = [];
  activateData = [];
  limit = 10;
  offset = 0;
  offsetPaginate = 0;
  page = 1;
  totalCount: number;
  sortData: any;
  sortName: any;
  sortBy: Sort = { name: 'name', value: 'ASC' };
  searchText = '';
  columns = [];
  ColumnMode = ColumnMode;
  showHelperText = false;
  statusActive: boolean;
  statusInActive: boolean;
  deBouncedInputValue = this.searchText;
  searchDebounce: Subject<string> = new Subject();
  people: Subject<string> = new Subject();
  @ViewChild('checkboxTemplate', { static: true }) checkboxTemplate: TemplateRef<any>;
  @ViewChild('createdTemplate', { static: true }) createdTemplate: TemplateRef<any>;
  @ViewChild('updatedTemplate', { static: true }) updatedTemplate: TemplateRef<any>;
  @ViewChild('headerTemplate', { static: true }) headerTemplate: TemplateRef<any>;
  @ViewChild('rowTemplate', { static: true }) rowTemplate: TemplateRef<any>;
  @ViewChild('dataTable', { static: true }) table: DatatableComponent;
  showBoth: boolean;
  subscription: Subscription = new Subscription();

  constructor(
    private languageList: LanguageService,
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private spinner: NgxSpinnerService,
    private toaster: ToastrService,
    private dataService: DataService,
    private ts: Title) {
    this.ts.setTitle('SEA Admin - Languages');
    const datatable$ = this.dataService.resizeDataTable.subscribe(data => {
      if (this.table) {
        this.resizeDataTable();
      }
    });
    this.subscription.add(datatable$);
  }

  ngOnInit() {
    this.columns = [
      {
        cellTemplate: this.checkboxTemplate,
        name: '',
        prop: 'id',
        sortable: false,
        minWidth: 75,
        maxWidth: 100
      },
      // { prop: 'index', name: '#', maxWidth: 100, sortable: false },
      { prop: 'name', name: 'Name', sortable: true , minWidth: 200,
        maxWidth: 400},
      { prop: 'identifier', name: 'Language Code', sortable: true, minWidth: 200,
        maxWidth: 250 },
      {
        cellTemplate: this.createdTemplate,
        name: 'Created At',
        prop: 'createdAt',
        sortable: true,
        minWidth: 200,
        maxWidth: 350,
      },
      {
        cellTemplate: this.updatedTemplate,
        name: 'Updated At',
        prop: 'updatedAt',
        sortable: true,
        minWidth: 200,
        maxWidth: 350,
      },
      {
        name: 'Status',
        prop: 'isActive',
        sortable: true,
        minWidth: 200,
        maxWidth: 350,
      },
      {
        cellTemplate: this.rowTemplate,
        headerTemplate: this.headerTemplate,
        name: 'Action',
        prop: 'id',
        sortable: false,
        maxWidth: 300,
        width: 160,
        minWidth: 200
      }

    ];

    const params$ = this.route.queryParams.subscribe((res) => {
      if (Object.keys(res).length) {
        this.searchText = res.search ? res.search : '';
        this.limit = res.limit ? Number(res.limit) : 10;
        this.offset = res.offset ? Number(res.offset) : 0;
        const sorting = res.sortName ? (this.sortBy.name = res.sortName, this.sortBy.value = res.sortValue) : {};
        this.getDataSource();
      } else {
        this.getDataSource();
      }
    });
    this.subscription.add(params$);
    this.setupSearchDeBouncer();
    this.offsetPaginate = this.offset / this.limit;
  }

  // Common Service
  getDataSource(offset?) {
    this.spinner.show();
    let search;
    this.searchText.length ? search = `&where=` + JSON.stringify({ name: { contains: this.searchText } }) : search = '';
    const languages$ = this.languageList.getRecord(this.limit, this.searchText ? offset : this.offset, this.sortBy, search)
      .subscribe((res: HttpResponse<any>) => {
        this.spinner.hide();
        this.records = res.body;
        this.totalCount = parseInt(res.headers.get('content-count'), 10);
        this.records = this.records.map((item: any, i) => {
          item.index = i + 1 + this.offset;
          return item;
        });

        this.records = this.records.map((result: any) => {
          if (result.isActive) {
            result.isActive = 'Active';
            this.activateData.push(result.id);
          } else {
            result.isActive = 'Inactive';
            this.InActivateData.push(result.id);
          }
          return result;
        });
      }, error => {
        this.spinner.hide();
        throw error;
      });
    this.subscription.add(languages$);
  }

  add() {
    this.router.navigate(['admin/languages/add']);
  }

  edit(id) {
    this.router.navigate([`admin/languages/edit/${id}`], {
      queryParams: {
        offset: this.offset,
      }
    });
  }

  // confirm pop-up section
  confirm(props) {
    this.statusActive = this.activateData.some(item => this.updateIDs.includes(item));
    this.statusInActive = this.InActivateData.some(item => this.updateIDs.includes(item));
    if (props === 'activate' && !this.statusActive) {
      this.callModal(props);
    } else if (props === 'deactivate' && !this.statusInActive) {
      this.callModal(props);
    } else if (props === 'deactivate' && (this.statusInActive && this.statusActive)) {
      this.callModal(props);
    } else if (props === 'activate' && (this.statusInActive && this.statusActive)) {
      this.callModal(props);
    } else {
      // this.changeDetectorRef.detectChanges();
      this.toaster.warning(`${this.updateIDs.length > 1 ? 'Languages' : 'Language'} Already ${props}d `);
      const checkBox = document.getElementsByName('check');
      checkBox.forEach((item: HTMLInputElement) => item.checked = false);
      this.updateIDs = [];
      // const deleteModal = this.modalService.open(ConfirmationWindowComponent);
      // deleteModal.componentInstance.title = ConstantData.deleteInfo.title;
      // deleteModal.componentInstance.content = ConstantData.deleteInfo.content;
      // deleteModal.componentInstance.recordId = props;
      // deleteModal.result.then(() => {
      //   this.remove(props);
      // }, (data) => { });
    }
  }

  // Calling Modal
  callModal(props) {
    if (props === 'activate') {
      const activateModal = this.modalService.open(ConfirmationWindowComponent);
      activateModal.componentInstance.title = ConstantData.ActivateInfo.title;
      activateModal.componentInstance.content = ConstantData.ActivateInfo.content;
      activateModal.componentInstance.updateState = props;
      activateModal.result.then((res: any) => {
        this.updateStatus(res);
      }, (data) => { });

    } else if (props === 'deactivate') {
      const deactivateModal = this.modalService.open(ConfirmationWindowComponent);
      deactivateModal.componentInstance.title = ConstantData.DeactivateInfo.title;
      deactivateModal.componentInstance.content = ConstantData.DeactivateInfo.content;
      deactivateModal.componentInstance.updateState = props;
      deactivateModal.result.then((res: any) => {
        this.updateStatus(res);
      }, (data) => { });

    } else {
      return false;
    }
  }

  // Bulk Update Selection
  checkRecords(template, value) {
    if (template.checked) {
      this.updateIDs.push(value.id);
    } else {
      const index = this.updateIDs.findIndex(item => item.id === value.id);
      this.updateIDs.splice(index, 1);
    }
  }

  // Bulk Update Logic
  updateStatus(updateStatus) {
    let selectedIDs;
    if (updateStatus === 'activate') {
      selectedIDs = this.updateIDs.filter(i => !this.records.filter(item => item.isActive === 'Active').map(itm => itm.id).includes(i));
    } else if (updateStatus === 'deactivate') {
      // tslint:disable-next-line: max-line-length
      selectedIDs = this.updateIDs.filter(i => !this.records.filter(item => item.isActive === 'Inactive').map(itm => itm.id).includes(i));
    }
    if (selectedIDs.length) {
      const payload = selectedIDs.map((item) => {
        return {
          id: item,
          isActive: updateStatus === 'activate' ? true : false,
        };
      });
      const languages$ = this.languageList.updateStatus(payload).subscribe((res) => {
        this.toaster.success(`${selectedIDs.length > 1 ? 'Languages' : 'Language'} ${updateStatus === 'deactivate' ? 'Deactivated' : 'Activated'}  Successfully`);
        const checkBox = document.getElementsByName('check');
        checkBox.forEach((item: HTMLInputElement) => item.checked = false);
        this.updateIDs = [];
        this.activateData = [];
        this.InActivateData = [];
        this.getDataSource();
      });
      this.subscription.add(languages$);
    }
  }

  remove(id) {
    this.spinner.show();
    const language$ = this.languageList.deleteRecord(id).subscribe(data => {
      this.spinner.hide();
      const index = this.records.findIndex(item => item.id === id);
      this.records.splice(index, 1);
      if (this.records.length) {
        this.records = [...this.records];
        this.records = this.records.map((item, i) => {
          item.index = i + 1 + this.offset;
          return item;
        });
      } else {
        this.offset = this.offset - 10;
        this.getDataSource();
      }
      this.toaster.success('Language deleted successfully');
    }, error => {
      this.spinner.hide();
      throw error;
    }
    );
    this.subscription.add(language$);
  }

  sort(event) {
    if (this.totalCount > 1) {
      this.sortBy.name = event.column.prop;
      this.sortBy.value = event.newValue;
      this.router.navigate(['admin/languages'], {
        queryParams: {
          limit: this.limit,
          offset: this.offset,
          sortName: this.sortBy.name,
          sortValue: this.sortBy.value
        }
      });
    } else {
      return false;
    }
  }

  paginate(event) {
    this.searchText = '';
    this.limit = event.limit;
    this.offset = this.limit * event.offset;
    this.offsetPaginate = event.offset;
    this.router.navigate(['admin/languages'], {
      queryParams: {
        limit: this.limit,
        offset: this.offset,
        sortName: this.sortBy.name,
        sortValue: this.sortBy.value
      }
    });
    const checkBox = document.getElementsByName('check');
    checkBox.forEach((item: HTMLInputElement) => item.checked = false);
    this.updateIDs = [];
    this.activateData = [];
    this.InActivateData = [];
  }

  onInputChange(e) {
    const searchText = e.target.value;
    if (!!searchText) {
      this.searchDebounce.next(searchText);
    }
  }

  setupSearchDeBouncer() {
    const search$ = this.searchDebounce.pipe(
      debounceTime(350),
      distinctUntilChanged(),
    ).subscribe((term: string) => {
      this.deBouncedInputValue = term;
      // this.searchText = term;
      const offset = 0;
      this.getDataSource(offset);
    });
    this.subscription.add(search$);
  }

  resizeDataTable() {
    this.records = this.records.map((item: any, i) => {
      item.index = i + 1 + this.offset;
      return item;
    });
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}
