.pl-20 {
    padding-left: 20%;
}
.display-block{
display: block !important;
}

::ng-deep{ 
    .ck-editor__editable_inline {
    max-height: 10%;
    min-height: 90%;
}
.ngx-dropdown-list-container{
    min-height: auto;
    max-height: 45vh;
    overflow: auto;
}
.list-add .modal-dialog .modal-content {
    height: auto;
}
}

.modal-body {
    flex: 1; /* Allow the body to take up the remaining space */
    padding: 1rem; /* Add padding for better spacing */
    height: 60vh; /* Set a fixed height or adjust as needed */
    overflow-y: auto; 
}
 
.scrollable-content {
    height: 100%; /* Adjust height as per your requirements */
    max-height: 60vh; /* Limit the maximum height */
    overflow-y: auto; /* Enable vertical scrolling */
    padding: 1rem; /* Optional: Add some padding for better spacing */
}