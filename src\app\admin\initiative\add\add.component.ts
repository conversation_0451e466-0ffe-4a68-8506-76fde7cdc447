import { Component, OnInit,ViewChild,ElementRef,<PERSON><PERSON><PERSON> } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, FormControl, FormArray, FormGroup, FormBuilder } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { BestPracticeService } from 'src/app/shared/services/best-practices.service';
// import { GooglePlaceDirective } from 'ngx-google-places-autocomplete';
import { CarboncurrencyService } from 'src/app/carbon/services/carboncurrency.service';
import { TopicsService } from 'src/app/carbon/services/topics.service';
import { SystemFieldsService } from 'src/app/shared/services/system-fields.service';
import { SeacCountryService } from 'src/app/shared/services/seac-country.service';
import { SiteService } from 'src/app/shared/services/site.service';

@Component({
  selector: 'app-add',
  templateUrl: './add.component.html',
  styleUrls: ['./add.component.scss']
})
export class AddComponent implements OnInit {
  @ViewChild('addressInput', { static: true }) addressInput!: ElementRef;

  initiativeForm: FormGroup;
  initiativeId: any;
  dialogInitiative: any;
  editInitiative = false;
  id: any;
  initcountry: any;
  serviceProviders: any = [];
  // opexOptions =[
  //   "0-10000 yearly",
  //   "0-5k yearly"
  //  ];
  opexOptions: any = [];
  opexconfig = {
    displayKey: 'fieldName',
    search: true,
    placeholder: 'Choose opEx Name',
    searchPlaceholder: 'Search opEx Name',
    height: '120px'
  };
  countries : [];
  Topics: any = [];
  initiativeconfig = {
    search: true,
    height: '400px',
    placeholder: 'Select the Country',
    displayKey: 'name',
  };
  serviceProviderconfig = {
    displayKey: 'fieldName',
    search: true,
    placeholder: 'Choose Select Provider',
    searchPlaceholder: 'Search Select Provider',
    height: '120px'
  };
  // opexconfig = {
  //   search: true,
  //   height: '400px',
  //   placeholder: 'Select the OpEx',
  //   displayKey: 'OpEx',
  // };
  serviceconfig = {
    search: true,
    height: '400px',
    placeholder: 'Select the Service',
    displayKey: 'name',
  };
  topicconfig = {
    search: true,
    height: '400px',
    placeholder: 'Select Topic',
    displayKey: 'name',
  }
  currencyconfig = {
    search: true,
    height: '400px',
    placeholder: 'Select the currency',
    displayKey: 'name',
  };
  options = {
    componentRestrictions: {
      country: []
    }
  };
  currencys: any = [];
  lat: any;
  lang: any;
  services: any = [];
  ManagerEmail: string;
  fieldType: any;

  selectedFiles: Array<{ 
    file?: File; 
    preview?: string; 
    name?: string; 
    data?: string; 
  }> = [];
  initiativeImageUrls: any = [];
  initiativeurlValue:any = [];
  isClickedFromHomepage: boolean = false;
  isFileSizeExceeded: boolean = false;
  siteCountry: string = '';

  // fileSizeError: string | null;
  maxFileSize: number = 5 * 1024 * 1024;   // 5MB

  constructor(
    private fb: FormBuilder,
    private practiceService: BestPracticeService,
    private toaster: ToastrService,
    private router: Router,
    private dialog: MatDialogRef<AddComponent>,
    private spinner: NgxSpinnerService,
    private currency: CarboncurrencyService,
    private topic: TopicsService,
    private  seaCountry: SeacCountryService,
    private systemFields: SystemFieldsService,
    private ngZone: NgZone,
    private route: ActivatedRoute,
    private siteService: SiteService,
  ) { 
    this.ManagerEmail =   sessionStorage.getItem('email');
  }

  ngOnInit() {

    if (this.editInitiative) {
      this.patchInitiativeForm();
    }
    this.createInitiativeForm();
    this.getCurrencyDetails();
    this.getServices();
    this.getTopics();
    this.getSystemFieldsList('Operating Expense');
    this.getSystemFieldsList('Service Provider');
    this.getSeaCountryList();
    const autocomplete = new google.maps.places.Autocomplete(this.addressInput.nativeElement, {
      types: ["locality"], // Specify the types as 'country' for country-specific results
    });

    // Add listener to handle address change
    autocomplete.addListener('place_changed', () => {
      this.ngZone.run(() => {
        const place = autocomplete.getPlace();
        // Handle the place details as needed
      });
    });
  }

  getSeaCountryList(){
    this.seaCountry.getCountryList().subscribe((res:any)=>{      
      this.countries = res;
    })
  }
  closeInitiative() {
    this.spinner.show();
    this.dialog.close();
    this.spinner.hide();
  }
  getCurrencyDetails() {
    this.currency.getList().subscribe((res: any) => {
      this.currencys = res.filter((curr: any) => curr.name);
    });
  }
  createInitiativeForm() {
    this.initiativeForm = this.fb.group({
      initiativeId: [''],
      initiativeName: [''],
      Phone: [''],
      Investment: [''],
      Saving: [''],
      SavingTime: [''],
      SavingPeriod: [''],
      Score: [''],
      Country: [''],
      Category: [''],
      Benefits: [''],
      ClientReferences: [''],
      Suitability: [''],
      ROI: [''],
      Implementation: [''],
      currency: ['', Validators.required],
      Address: [''],
      description: [''],
      lattitute: [''],
      langitute: [''],
      service: [''],
      serviceprovider: ['', Validators.required],
      opex: ['', Validators.required],
      topic: [''],
      impact: [''],
      initiativeurl: this.fb.array([]),
    })

    if(this.siteCountry){
      this.initiativeForm.controls['Country'].setValue(this.siteCountry);
      this.initiativeForm.controls['Country'].disable();
    }
  }
  patchInitiativeForm() {
    if (this.id) {
      this.practiceService.getOneIntiativeData(this.id).subscribe((res: any) => {        
        if(res.initiativeurl){
          res.initiativeurl.map((val: any) => {
            const payload:any = {
              preview: val.imageUrl,
              file: {
                name: val.fileName
              }
            }
            this.selectedFiles.push(payload)
          })     
        }
        this.initcountry = res.country;
        this.initiativeurlValue = res.initiativeurl;
        this.initiativeForm.patchValue({
          initiativeId: res.initiativeid || '',
          initiativeName: res.initiativename || '',
          Country: res.country || '',
          initiativeAddress: res.address || '',
          Phone: res.contact || '',
          Investment: res.investment || '',
          Saving: res.saving || '',
          SavingTime: res.time || '',
          SavingPeriod: res.period || '',
          Score: res.score || '',
          Address: res.address || '',
          description: res.description || '',
          Category: res.category || '',
          Benefits: res.benefits || '',
          ClientReferences: res.clientreferences || '',
          Suitability: res.suitability || '',
          ROI: res.ROI || '',
          Implementation: res.implementation || '',
          lattitute: res.lat,
          langitute: res.lang,
          currency: res.currency,
          serviceprovider: res.serviceprovider,
          service: res.service,
          opex: res.opEx,
          topic: res.topic,
          impact: res.impact,
        });
      });
    }
  }
  getSystemFieldsList(fieldType){
    this.systemFields.getSystemFields(fieldType).subscribe((res:any)=>{
      if (fieldType == 'Operating Expense') {
        this.opexOptions = [...res];
      } else if (fieldType == 'Service Provider') {
        this.serviceProviders = [...res];
      }
      
    },(error)=>{
    })
  }
  getServices(){
    this.currency.getServiceList().subscribe((res: any) => {
      this.services = res;
    })
  }
  getTopics(){
    this.topic.getTopic().subscribe(res => {
    this.Topics = res;    
    },(e) => {      
    })
  }
  addInitiative() {
    const language = {
        'en': {},
        'fr': {},
        'es': {}
    };
    const langu = 'en';
    
    // Prepare the files to be uploaded
    var files = this.selectedFiles.map(fileObj => ({
        name: fileObj.name,
        data: fileObj.data  // Base64 data
    }));

    var files = files.filter(item => item.name !== undefined && item.data !== undefined);
    
    // Call the image upload function
    this.practiceService.seaStoreImageUpload({ Data: files }).subscribe((res: any) => {

        // Store the uploaded image URLs
        this.initiativeImageUrls = res.imageUrls;

        // Prepare the payload after successfully uploading images
        const payload: any = {
            initiativeid: this.initiativeForm.value.initiativeId || '',
            initiativename: this.initiativeForm.value.initiativeName || '',
            country: this.initiativeForm.value.Country ? this.initiativeForm.value.Country.map(int => int.id) : [],
            address: this.initiativeForm.value.Address || '',
            contact: this.initiativeForm.value.Phone || '',
            investment: this.initiativeForm.value.Investment || '',
            saving: this.initiativeForm.value.Saving || '',
            time: this.initiativeForm.value.SavingTime || '',
            period: this.initiativeForm.value.SavingPeriod || '',
            score: this.initiativeForm.value.Score || 0,
            description: this.initiativeForm.value.description || '',
            category: this.initiativeForm.value.Category || '',
            benefits: this.initiativeForm.value.Benefits || '',
            clientreferences: this.initiativeForm.value.ClientReferences || '',
            suitability: this.initiativeForm.value.Suitability || '',
            ROI: this.initiativeForm.value.ROI || '',
            implementation: this.initiativeForm.value.Implementation || '',
            lat: this.initiativeForm.value.lattitute || '',
            lang: this.initiativeForm.value.langitute || '',
            currency: this.initiativeForm.value.currency.id || '',
            opEx: this.initiativeForm.value.opex.id || '',
            serviceprovider: this.initiativeForm.value.serviceprovider.id || '',
            service: this.initiativeForm.value.service ? this.initiativeForm.value.service.map(int => int.id) : [],
            topic: this.initiativeForm.value.topic?.id,
            impact: this.initiativeForm.value.impact,
        };
        // Create a set of file names from selectedFiles for quick lookup
        const selectedFileNames = new Set(this.selectedFiles.map((fileObj:any) => fileObj.file.name));

        // Filter initiativeurlValue based on matching file names
        if(this.initiativeurlValue){
          var matchedInitiatives = this.initiativeurlValue.filter((item:any) => selectedFileNames.has(item.fileName));
        }
        // Set language data
        if (langu === 'en') {
            language['en'] = { ...payload };
        } else if (langu === 'fr') {
            language['fr'] = {};
        } else if (langu === 'es') {
            language['es'] = {};
        }
        payload.language = language;

        // Determine if it's a new record or an update
        if (this.id) {
            payload.initiativeurl = matchedInitiatives ? matchedInitiatives.concat(this.initiativeImageUrls) : this.initiativeImageUrls;
            payload.updatedAt = Date.now();
            this.spinner.show();
            this.insertUpdateRecord(this.id, payload);
        } else {
          // Include image URLs if available
          if (this.initiativeImageUrls.length != 0) {
            payload.initiativeurl = this.initiativeImageUrls;
            }
            this.spinner.show();
            payload.createdAt = Date.now();
            this.insertUpdateRecord('', payload);
        }

        this.spinner.hide();
        this.closeInitiative();

    }, (error) => {
        // Handle error case for image upload
        this.toaster.error(error.message);
        this.spinner.hide();
    });
  }

  insertUpdateRecord(id: any, payload: any) {
    if (id) {
      this.practiceService.updateIntiativeData(payload, id).subscribe((res: any) => {
        // this.updateBestPracitice(res.id);
        this.toaster.success('Updated Initiative');
      }, (error) => {
        this.toaster.error(error.message);
        this.spinner.hide();
      });

    } else {
      payload.createdBy = this.ManagerEmail || '';
      this.practiceService.createIntiativeData(payload).subscribe((res: any) => {
        this.initiativeId = res.id;
        this.toaster.success('Added Initiative');
      }, (error) => {
        this.toaster.error(error.message);
        this.spinner.hide();
      });
    }
  }
  handleAddressChange(address: any, type, value?) {
    if (address.formatted_address) {   
      this.initiativeForm.patchValue({
        Address: address.formatted_address || '',
        lattitute: address.geometry.location.lat() || '',
        langitute: address.geometry.location.lng() || ''
      });
    } else {
      this.initiativeForm.patchValue({
        Address: '',
        lattitute: '',
        langitute: ''
      });
    }
  }
  initiativeurl(): FormArray {        
    return this.initiativeForm.get('initiativeurl') as FormArray;
  }
  newInitiativeurl(initiative?): FormGroup {
    return this.fb.group({
      imageUrl: initiative?.imageUrl ? initiative?.imageUrl : '',
    });
  }
  addNewinitiativeurl(initiative?: any){   
    if (initiative && initiative.length) {
      initiative.map((val: any) => this.initiativeurl().push(this.newInitiativeurl(val)));
    } else {
      this.initiativeurl().push(this.newInitiativeurl());
    }
  }
  removeinitiativeurl(empIndex: number) {
    this.initiativeurl().removeAt(empIndex);
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      // this.selectedFiles = []; // Reset previous selections
      Array.from(input.files).forEach((file) => {
        if(file.size <= this.maxFileSize){
          this.isFileSizeExceeded = false;
          const reader = new FileReader();
          reader.onload = (e: any) => {
            this.selectedFiles.push({
              file, // The original File object
              preview: e.target.result, // Base64 preview for display
              name: file.name, // Store file name if needed
              data: e.target.result.split(',')[1] // Base64 data without the prefix
            });
          };
          reader.readAsDataURL(file);
        }
        else{
          this.isFileSizeExceeded = true;
        }
      });
    }
  }
  

  removeFile(index: number): void {
    this.selectedFiles = this.selectedFiles.filter((_, i) => i !== index);
  }
}
