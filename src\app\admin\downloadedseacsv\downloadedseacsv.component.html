<section class="my-3 p-3">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-4 question-text">
                <h2 style="color:rgb(42,41,92);">File Download Status</h2>
            </div>
            <div class="col-md-8">
                <div class="row justify-content-end d-flex">
                    <div class="col-md-4 col-12 search">
                        <input type="text" class="form-control" placeholder="Search by SiteId" [(ngModel)]="searchField"
                            (input)="search($event)">
                    </div>
                    <div class="col-md-3 col-12 d-flex justify-content-md-end justify-content-center mt-3 mt-md-0">
                        <button placement="bottom" ngbTooltip="Export" class="btn btn-primary changfont" (click)="Export()" type="submit">
                            <i class="fa fa-upload d-inline fa-sm mr-1"></i>
                            <span class="changfont">Export</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="">
    <div class="col-md-12" style="padding: 5px 30px; min-inline-size: 500px;">
        <div class="row form-group">
            <div class="col-md-12">
                <!-- Wrap the table in a div with overflow-x:auto for responsive scrolling -->
                <div class="table-responsive">
                    <ngx-datatable class="bootstrap cursor-pointer" [rows]="records" [columnMode]="ColumnMode.force"
                        [headerHeight]="headerHeight" [footerHeight]="footerHeight" [rowHeight]="rowHeight" [limit]="limit"
                        (page)="paginate($event)" [sorts]="[{prop: 'name', dir: 'asc'}]" (sort)="sort($event)" [offset]="offset"
                        [count]="totalCount" [externalSorting]="true" [externalPaging]="true" style="width: 100%; max-width: 100%;">
                        <ngx-datatable-column name="Site" prop="site" [maxWidth]="150" [minWidth]="80">
                            <ng-template let-value="value" ngx-datatable-cell-template>
                                <span>{{value || '-'}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column name="Site Id" prop="siteId" [maxWidth]="200" [minWidth]="180">
                            <ng-template let-value="value" ngx-datatable-cell-template>
                                <span>{{value || '-'}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column name="Date" prop="Date" [maxWidth]="300" [minWidth]="150">
                            <ng-template let-value="value" ngx-datatable-cell-template>
                                <span>{{value || '-'}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column name="Downloaded By" prop="downloadedBy" [maxWidth]="350" [minWidth]="280">
                            <ng-template let-value="value" ngx-datatable-cell-template>
                                <span>{{value || '-'}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column name="URL" prop="url" [maxWidth]="600" [minWidth]="280">
                            <ng-template let-value="value" ngx-datatable-cell-template>
                                <span>{{value || '-'}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column name="Type" prop="fileType" [maxWidth]="200" [minWidth]="100">
                            <ng-template let-value="value" ngx-datatable-cell-template>
                                <span>{{value || '-'}}</span>
                            </ng-template>
                        </ngx-datatable-column>
                        <ngx-datatable-column name="Action" prop="Action" [maxWidth]="100" [minWidth]="100">
                            <ng-template let-row="row" ngx-datatable-cell-template>
                                <span class="d-flex" style="padding: 4px;">
                                    <i class="fas fa-download" (click)="download(row)"
                                        style="color:rgb(42,41,92);padding-inline-end: 10px;"></i>
                                </span>
                            </ng-template>
                        </ngx-datatable-column>
                    </ngx-datatable>
                </div>
            </div>
        </div>
    </div>
</section>
